from conans import ConanFile, tools
import os


class PackageConan(ConanFile):
  settings = ()
  license = 'Momenta, Inc'
  description = 'Time Manager'
  settings = 'os', 'compiler', 'build_type', 'arch'
  url = 'https://devops.momenta.works/Momenta/mf/_git/time_manager'
  generators = 'cmake'

  requires = ('mf_mfruntime/auto@momenta/stable',
              'obf_time_manager_sdk/auto@momenta/stable',
              'maf_interface/auto@momenta/stable',
              'obf_em_interface/auto@momenta/stable')

  build_requires = ('google_test/auto@momenta/stable')

  def imports(self):
    self.copy('*.so*', dst='deploy/common/lib', src='lib')
    self.copy('*', dst='deploy/common/tools', src='tools')
    self.copy('*', dst='deploy/common/python', src='python')
    self.copy('*', dst='deploy/common/bash', src='bash')
    self.copy('*', dst='deploy/common/bin', src='bin')

  def requirements(self):
    env_id = os.environ.get('BENV_ID')
    # NOTE: == to != to test isolation
    if env_id == "refcar_8620P_qnx_with_sdp_710":
      self.requires("obf_cm/auto@momenta/stable")
      self.requires("cpp_httplib/auto@momenta/stable")
      self.requires("obf_dm_interface/auto@momenta/stable")
    elif env_id in [
        "refcar_8650P_qnx_with_sdp_710", "bgans_8650_qnx", "TMAS_f_QC8650P_QNX"
    ]:
      self.requires("obf_cm/auto@momenta/stable")
      self.requires("cpp_httplib/auto@momenta/stable")
      self.requires("obf_dm_interface/auto@momenta/stable")
    elif env_id == "qwangs_8650P_qnx_with_sdp_710" or env_id == "wys_qc8775_qnx":
      self.requires("obf_cm/auto@momenta/stable")
      self.requires("cpp_httplib/auto@momenta/stable")

  def package(self):
    source_dir = os.environ.get('PACKAGE_SOURCE_DIR')
    build_dir = os.environ.get('PACKAGE_BUILD_DIR')
    install_dir = os.environ.get('PACKAGE_INSTALL_DIR')
    self.copy('*',
              dst='include',
              src='{}/include'.format(install_dir),
              symlinks=True)
    self.copy('*', dst='lib', src='{}/lib'.format(install_dir), symlinks=True)
    self.copy('*', dst='bin', src='{}/bin'.format(install_dir), symlinks=True)

  def package_info(self):
    self.cpp_info.libs = tools.collect_libs(self)
