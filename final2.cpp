src/communication/someip_time_channel.cpp
#include "someip_time_channel.h"
#include "someip_data_coverter.hpp"
#include "tmlog.h"
#include "wt_serializer.h"

namespace tmgr {

int32_t SomeIPTimeChannel::last_diff_seconds = 0;
int32_t SomeIPTimeChannel::last_diff_nanoseconds = 0;

SomeIPTimeChannel::SomeIPTimeChannel(
    std::shared_ptr<WTStatusChecker> wt_status_checker,
    std::shared_ptr<SystemClockManager> system_clock_manager)
    : wt_status_checker_(wt_status_checker),
      system_clock_manager_(system_clock_manager) {}

SomeIPTimeChannel::~SomeIPTimeChannel() { proxy_.TimeDiffEvent.Unsubscribe(); }

bool SomeIPTimeChannel::init() {
  proxy_.TimeDiffEvent.SetReceiveHandler([this] {
    proxy_.TimeDiffEvent.GetNewSamples([this](const std::shared_ptr<
                                              obf::cm::TimeSyncStruct>
                                                  sample) {
      // NOTE: choose diff according to environment variable
      static bool use_onboard_diff = []() {
        const char *env_p = std::getenv("ONBOARD_SENSORS_ENABLED");
        if (env_p != nullptr && std::string(env_p) == "0") {
          LOG_INFO("[SomeIPDataConverter]: Use Vehicle Time Diff from MCU");
          return false;
        }
        LOG_INFO("[SomeIPDataConverter]: Use Onboard Time Diff from MCU");
        return true;
      }();

      WTInfo info =
          SomeIPDataConverter::convertToWTInfo(*sample, use_onboard_diff);

      bool source_is_valid = false;

      if (use_onboard_diff) {
        source_is_valid = sample->time_diff_onbard.wt_sync_status == 0;
      } else {
        source_is_valid = sample->time_diff_vehicle.wt_sync_status == 0;
      }

      int32_t change_in_seconds = info.diff_seconds - last_diff_seconds;
      int32_t change_in_nanoseconds =
          info.diff_nanoseconds - last_diff_nanoseconds;

      last_diff_seconds = info.diff_seconds;
      last_diff_nanoseconds = info.diff_nanoseconds;

      const char *diff_source = use_onboard_diff ? "onboard" : "vehicle";

      LOG_INFO("[SomeIPTimeChannel]: receive time sync event from [%s]: "
               "ot_syncStatus is %d, diff_seconds is %d "
               "(%s%d), "
               "diff_nanoseconds is %d (%s%d)",
               diff_source, info.ot_master_sync_status.load(),
               info.diff_seconds.load(), (change_in_seconds > 0 ? "+" : ""),
               change_in_seconds, info.diff_nanoseconds.load(),
               (change_in_nanoseconds > 0 ? "+" : ""), change_in_nanoseconds);

      if (source_is_valid) {
        // make sure only one thread will be spawned to update system time
        if (is_need_update_system_time.load() && !is_thread_active_.load()) {
          is_thread_active_.store(true);
          // TODO: use std::thread or not to avoid blocking the event callback,
          // this can be discussed later
          std::thread([this, info]() {
            // SystemClock need use this first diff to set its system clock if
            // no record in file
            int64_t total_diff_nanoseconds =
                info.diff_seconds * 1000000000LL + info.diff_nanoseconds;

            auto timeGap = WTSerializer::getInstance().timeGap();
            auto wtSyncMode = WTSerializer::getInstance().wtSyncMode();

            LOG_INFO("[SomeIPTimeChannel]: wtSyncMode is %s, add "
                     "total_diff_nanoseconds: %lld(ns) with gap value(normal "
                     "mode will ignore it) %lld(ns) "
                     "to %lld(ns)",
                     wtSyncMode == WTSyncMode::WT_SYNC_MODE_NORMAL ? "Normal"
                                                                   : "Gap",
                     total_diff_nanoseconds, timeGap,
                     total_diff_nanoseconds + timeGap);

            if (wtSyncMode == WTSyncMode::WT_SYNC_MODE_GAP) {
              total_diff_nanoseconds += timeGap;
            }

            if (system_clock_manager_->updateSystemClockWithWTInfo(
                    total_diff_nanoseconds)) {
              is_need_update_system_time.store(false);
              LOG_INFO("[SomeIPTimeChannel]: ----------- update system clock "
                       "first time startup finished --------- (check wt "
                       "time with current system_time choose the bigger one)");
            } else {
              LOG_ERR(
                  "[SomeIPTimeChannel]: ----------- update system clock first "
                  "time startup failed -----------, because "
                  "cannot get ot time, maybe TimeSync is not startup, will "
                  "wait for next coming message with valid ot time");
            }
            is_thread_active_.store(false);
          })
              .detach();
        }

        wt_status_checker_->feedWTInfo(info);
      } else {
        LOG_WARN("[SomeIPTimeChannel]: this time sync event is invalid which "
                 "mcu's feed wt_status is 1, ignore it");

        // even we review invalid values, but we should not treet it as timeout
        wt_status_checker_->feedWTInfoDog();
      }
    });
  });

  proxy_.TimeDiffEvent.Subscribe(10);

  LOG_INFO("[SomeIPTimeChannel]: init proxy / subscribe event success");
  return true;
}

} // namespace tmgr

src/communication/someip_time_channel.h
#pragma once

#include "service/TimeManager/TimeManagerService_proxy.h"
#include "system_clock_manager.h"
#include "wt_statuschecker.h"
#include <atomic>

namespace tmgr {

class SomeIPTimeChannel {
public:
  SomeIPTimeChannel(std::shared_ptr<WTStatusChecker> wt_status_checker,
                    std::shared_ptr<SystemClockManager> system_clock_manager);
  ~SomeIPTimeChannel();
  bool init();

private:
  // NOTE: proxy constructor is synchronous, where will be a block point when
  // proxy create is waiting
  obf::cm::proxy::TimeManager_TimeManagerService::TimeManagerService_Proxy
      proxy_{};
  obf::cm::TimeSyncStruct time_sync_struct;
  std::shared_ptr<WTStatusChecker> wt_status_checker_;
  std::shared_ptr<SystemClockManager> system_clock_manager_;
  std::atomic<bool> is_thread_active_{false};
  std::atomic<bool> is_need_update_system_time{true};
  static int32_t last_diff_seconds;
  static int32_t last_diff_nanoseconds;
};
} // namespace tmgr

src/st/system_clock_manager.cpp
#include "system_clock_manager.h"
#include "build_time_generator.h"
#include "data_types.h"
#include "ot_consumer.h"
#include "system_clock_utility.h"
#include "tmlog.h"
#include "wt_calculator.h"
#include <chrono>
#ifdef TM_USE_DM
#include "monitor/dm_monitor.h"
#endif

namespace tmgr {
SystemClockManager::SystemClockManager(const std::string &filePath,
                                       int diskWriteIntervalMillSec)
    : timeFileHandler_(filePath),
      diskWriteIntervalMillSec_(diskWriteIntervalMillSec) {
  initSystemTime();
  startDiskWriteTimer();
}

// TODO: just check first flag and feed value
bool SystemClockManager::updateSystemClockWithWTInfo(
    int64_t diff_in_nanoseconds) {
  if (needFirstTimeUpdateSystemTime_) {
    LOG_INFO(
        "[SystemClockManager]: begin to update system time with wt diff(ns) "
        "%lld from someip",
        diff_in_nanoseconds);

    auto ot_time = OTConsumer::getInstance().GetCurrentTime();

    if (ot_time.time_since_epoch().count() <= 0) {
      LOG_ERR("[SystemClockManager]: OT time is invalid which is <= 0, need "
              "wait for "
              "TimeSyncDaemon back to normal");
      return false;
    }

    // make sure OT time must be valid
    auto wt_diff_duration_nano = std::chrono::nanoseconds(diff_in_nanoseconds);
    obf::ts::Timestamp newTimePoint = ot_time + wt_diff_duration_nano;
    LOG_INFO("newTimePoint calculate: %lld, which is %lld(ot) + %lld(diff)",
             newTimePoint.time_since_epoch().count(),
             ot_time.time_since_epoch().count(), wt_diff_duration_nano.count());

    auto newSystemTime = Timestamp(
        std::chrono::nanoseconds(newTimePoint.time_since_epoch().count()));

    // NOTE: cannot change high_resolution_clock to system_clock to try to
    // compile because in QNX, high_resolution calculation process is not the
    // same as system_clock and also you cannot revert high_resolution_clock
    // back to system_clock, so in this place we can just use system_clock and
    // ignore nanoseconds part to calculate with newTimePoint
    auto currentSystemTime =
        Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::system_clock::now().time_since_epoch()));
    // when wt diff + ot > current system time, update system time once
    if (newSystemTime > currentSystemTime) {
      LOG_INFO(
          "[SystemClockManager]: wtTime %lld (%s) is bigger than current "
          "system time %lld (%s), begin to update system time with wt time",
          newSystemTime.time_since_epoch().count(),
          SystemClockUtility::prettyStrTimePoint(newSystemTime).c_str(),
          currentSystemTime.time_since_epoch().count(),
          SystemClockUtility::prettyStrTimePoint(currentSystemTime).c_str());
      setSystemClock(newSystemTime);
    } else {
      LOG_INFO(
          "[SystemClockManager]: wtTime %lld (%s) is less than current system "
          "time %lld (%s), no need to update system time",
          newSystemTime.time_since_epoch().count(),
          SystemClockUtility::prettyStrTimePoint(newSystemTime).c_str(),
          currentSystemTime.time_since_epoch().count(),
          SystemClockUtility::prettyStrTimePoint(currentSystemTime).c_str());
    }
    needFirstTimeUpdateSystemTime_ = false;
  } else {
    LOG_INFO("[SystemClockManager]: no need to update system time, has setted");
  }

  return true;
}

void SystemClockManager::setSystemClock(const Timestamp &timePoint) {
  // TODO: check if equal 0 is valid or not
  if (timePoint.time_since_epoch().count() <= 0) {
    LOG_ERR("[SystemClockManager]: timePoint less/equal than 0, invalid value, "
            "no need to "
            "set system time");
    return;
  }

  // as sometimes, TimeManager will restart serveal times beyond 1970 for
  // testing cases, we should try to note let time back to protect for infinite
  // waiting
  auto currentSysTime =
      Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
          std::chrono::system_clock::now().time_since_epoch()));

  // check currentSysTime is newer that timePoint, system time *cannot* be
  // rolled back
  if (currentSysTime > timePoint) {
    LOG_WARN("[SystemClockManager]: current system time %lld (GMT %s) is newer "
             "than timePoint %lld (GMT %s), "
             "no need to set system time",
             currentSysTime.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(currentSysTime).c_str(),
             timePoint.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(timePoint).c_str());
  } else {
    LOG_INFO("[SystemClockManager]: after checking timepoint validity, begin "
             "to set system "
             "clock from %lld (GMT %s) to %lld (GMT %s)",
             currentSysTime.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(currentSysTime).c_str(),
             timePoint.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(timePoint).c_str());
    systemTimeSetter_.setSystemTime(timePoint);
  }
}

// NOTE: this will seralize WT time into disk not system clock
void SystemClockManager::startDiskWriteTimer() {
  diskWriteTimer_.start(diskWriteIntervalMillSec_, [this]() {
    auto time_point = WTCalculator::calculateWTTime(true);

#ifdef TM_USE_DM
    // for DTC purpose
    checkOTTimeValid();
    checkWTTimeValid(time_point.time_since_epoch().count());
#endif

    auto tp_count = time_point.time_since_epoch().count();
    if (tp_count <= 0) {
      LOG_WARN(
          "[SystemClockManager]: timePoint less/equal than 0, which is %lld, "
          "invalid value, no need to "
          "write to file",
          tp_count);
      return;
    }

    LOG_INFO("[SystemClockManager]: begin to write timestamp(ns) to file %s "
             "with count "
             "%lld, GMT time is %s",
             timeFileHandler_.path().c_str(),
             time_point.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(time_point).c_str());

    timeFileHandler_.writeTime(time_point);
  });
}
void SystemClockManager::checkOTTimeValid() {
#ifdef TM_USE_DM
  auto ot_time = OTConsumer::getInstance().GetCurrentTime();
  if (DMMonitor::getInstance().isDMEnabled()) {
    if (ot_time.time_since_epoch().count() <= 0) {
      DMMonitor::getInstance().reportDMErrorCode(
          DMErrorCode::DM_ERR_TM_GET_OT_ERROR, true);
    } else {
      DMMonitor::getInstance().reportDMErrorCode(
          DMErrorCode::DM_ERR_TM_GET_OT_ERROR, false);
    }
  }
#endif
}

void SystemClockManager::checkWTTimeValid(int64_t nano_count) {
#ifdef TM_USE_DM
  // we should compare nano_count to 1980, if we receive a time point less than
  // it, we should treat it as error
  static const int64_t NANO_1980 = 315532800ULL * 1000000000ULL;

  if (DMMonitor::getInstance().isDMEnabled()) {
    if (nano_count <= NANO_1980) {
      DMMonitor::getInstance().reportDMErrorCode(
          DMErrorCode::DM_ERR_TM_UTC_INVALID, true);
    } else {
      DMMonitor::getInstance().reportDMErrorCode(
          DMErrorCode::DM_ERR_TM_UTC_INVALID, false);
    }
  }
#endif
}

SystemClockManager::~SystemClockManager() { diskWriteTimer_.stop(); }

void SystemClockManager::initSystemTime() {
  // read from file
  if (timeFileHandler_.hasValidTime()) {
    LOG_INFO("[SystemClockManager]: begin to read time from file, and set it "
             "into sys");
    setSystemClock(timeFileHandler_.readTime());
  } else {
    // read from build time
    LOG_INFO("[SystemClockManager]: begin to read time from building time, and "
             "set it "
             "into sys");

    auto buildTimePoint = BuildTimeGenerator::getBuildTime();

    LOG_INFO("[SystemClockManager]: build time is %lld, pretty print is %s",
             buildTimePoint.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(buildTimePoint).c_str());

    auto timestamp =
        Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
            buildTimePoint.time_since_epoch()));

    setSystemClock(timestamp);
  }
}

bool SystemClockManager::isTimePointFutureThanSystemClock(
    const Timestamp &timePoint) {
  auto currentSystemTime =
      Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
          std::chrono::system_clock::now().time_since_epoch()));
  // when wt diff + ot > current system time, update system time once
  if (timePoint > currentSystemTime) {
    return true;
  } else {
    return false;
  }
}

} // namespace tmgr


src/st/system_clock_manager.h
#pragma once

#include "common/timer.hpp"
#include "data_types.h"
#include "system_clock_setter.h"
#include "time_file_handler.h"

namespace tmgr {

class SystemClockManager {
public:
  SystemClockManager(const std::string &filePath, int diskWriteIntervalMillSec);
  ~SystemClockManager();

  // TODO: just check first flag
  // TODO: need update with someip logic for refresh system clock
  bool updateSystemClockWithWTInfo(int64_t diff_in_nanoseconds);
  static bool isTimePointFutureThanSystemClock(const Timestamp &timePoint);

private:
  void setSystemClock(const Timestamp &timePoint);
  void startDiskWriteTimer();
  void initSystemTime();
  void checkOTTimeValid();
  void checkWTTimeValid(int64_t nano_count);

private:
  TimeFileHandler timeFileHandler_;
  std::string filePath_;
  int diskWriteIntervalMillSec_;
  Timer diskWriteTimer_;
  SystemClockSetter systemTimeSetter_;
  bool needFirstTimeUpdateSystemTime_{
      true}; // this flag can be toggle to meet different requirement
};

} // namespace tmgr

src/st/system_clock_setter.cpp
#include "system_clock_setter.h"
#include "data_types.h"
#include "system_clock_utility.h"
#include "tmlog.h"
#include <cstring>
#include <sys/time.h>

namespace tmgr {

bool SystemClockSetter::setSystemTime(const Timestamp &timestamp) {
  if (timestamp.time_since_epoch().count() <= 0) {
    LOG_ERR("[SystemTimeSetter]: set system time failed with invalid timestamp "
            "which count is <= 0");
    return false;
  }
  auto timePoint =
      SystemClockUtility::convertTimestampNsToSystemClockUs(timestamp);
  std::time_t newTime = std::chrono::system_clock::to_time_t(timePoint);

  auto microsecondsPart = std::chrono::duration_cast<std::chrono::microseconds>(
                              timestamp.time_since_epoch()) %
                          1000000;

  struct timeval tv;
  tv.tv_sec = newTime;
  tv.tv_usec = static_cast<suseconds_t>(microsecondsPart.count());

  if (settimeofday(&tv, NULL) != 0) {
    LOG_ERR("[SystemTimeSetter]: set system time failed with seconds part is "
            "%lld and "
            "microseconds part is %lld, err reason is %s",
            newTime, microsecondsPart.count(), strerror(errno));

    return false;
  } else {

    LOG_INFO(
        "[SystemTimeSetter]: set system time success with seoncds part is %lld "
        "and microseconds part is %lld (%s)",
        newTime, microsecondsPart.count(),
        SystemClockUtility::prettyStrTimePoint(timestamp).c_str());

    return true;
  }
}

bool SystemClockSetter::setSystemTime(
    const std::chrono::system_clock::time_point &timePoint) {
  auto tm = SystemClockUtility::convertSystemClockUsToTimestampNs(timePoint);
  return setSystemTime(tm);
}

} // namespace tmgr

src/st/system_clock_setter.h
#pragma once

#include "data_types.h"
#include <chrono>

namespace tmgr {

struct SystemClockSetter {
public:
  static bool setSystemTime(const Timestamp &timePoint);
  static bool
  setSystemTime(const std::chrono::system_clock::time_point &timePoint);
};

} // namespace tmgr