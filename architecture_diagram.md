# Time Manager Architecture Diagram

```mermaid
graph TB
    %% External Systems
    MCU[MCU<br/>Time Source]
    GNSS[GNSS/ICB<br/>Gap Source]
    HTTPServer[HTTP Time Server]
    UDPClient[UDP Time Client]
    
    %% Communication Layer
    subgraph CommLayer["Communication Layer"]
        SOMEIP[SOME/IP Time Channel<br/>someip_time_channel.cpp]
        UDP[UDP Time Channel<br/>udp_time_channel.cpp<br/>*********:2381]
        HTTP[HTTP Time Channel<br/>http_time_channel.cpp]
        GapChannel[Gap Channel<br/>someip_time_gap_channel.cpp]
    end
    
    %% Core Processing Layer
    subgraph CoreLayer["Core Processing Layer"]
        TM[Time Manager<br/>time_manager.cpp<br/>Main Daemon]
        OTConsumer[OT Consumer<br/>Time Calculation]
        StatusChecker[WT Status Checker<br/>wt_statuschecker.cpp<br/>Validation & Sync]
    end
    
    %% System Management Layer
    subgraph SystemLayer["System Management Layer"]
        SysClockMgr[System Clock Manager<br/>system_clock_manager.cpp<br/>5s Threshold Multi-Sync]
        SysClockSetter[System Clock Setter<br/>system_clock_setter.cpp]
        RunModeMgr[Running Mode Manager<br/>running_mode_manager.cpp]
    end
    
    %% Monitoring Layer
    subgraph MonitorLayer["Monitoring & Diagnostics"]
        HeartbeatMon[Heartbeat Monitor<br/>heartbeat_monitor.cpp]
        DMMon[DM Monitor<br/>dm_monitor.cpp]
        DIDReporter[DID Reporter<br/>did_reporter.cpp]
    end
    
    %% Storage Layer
    subgraph StorageLayer["Storage & IPC"]
        SharedMem[Shared Memory<br/>WTInfo Atomic Data<br/>Thread-Safe IPC]
        ConfigMgr[Config Manager<br/>TimeManagerConfig]
        Logging[File Logging<br/>Audit Trails]
    end
    
    %% System Clock
    SystemClock[System Clock<br/>OS Time]
    
    %% Data Flow Connections
    MCU --> SOMEIP
    GNSS --> GapChannel  
    HTTPServer --> HTTP
    UDPClient --> UDP
    
    SOMEIP --> OTConsumer
    UDP --> OTConsumer
    HTTP --> OTConsumer
    GapChannel --> StatusChecker
    
    TM --> RunModeMgr
    TM --> StatusChecker
    TM --> SysClockMgr
    
    OTConsumer --> StatusChecker
    StatusChecker --> SharedMem
    StatusChecker --> SysClockMgr
    
    SysClockMgr --> SysClockSetter
    SysClockSetter --> SystemClock
    
    RunModeMgr --> SharedMem
    SharedMem --> HeartbeatMon
    
    StatusChecker --> DMMon
    DMMon --> DIDReporter
    
    SharedMem --> Logging
    ConfigMgr --> TM
    
    %% Mode Annotations
    classDef communication fill:#e1f5fe
    classDef core fill:#f3e5f5  
    classDef system fill:#e8f5e8
    classDef monitor fill:#fff3e0
    classDef storage fill:#fce4ec
    
    class SOMEIP,UDP,HTTP,GapChannel communication
    class TM,OTConsumer,StatusChecker core
    class SysClockMgr,SysClockSetter,RunModeMgr system
    class HeartbeatMon,DMMon,DIDReporter monitor
    class SharedMem,ConfigMgr,Logging storage
```

## Time Synchronization Formula

### Normal Mode
```
WT_Time = OT_Time + MCU_diff
```

### Gap Mode  
```
WT_Time = OT_Time + MCU_diff + ICB_gap
```

## Key Data Structures

```cpp
struct WTInfo {
    std::atomic<int32_t> diff_seconds;
    std::atomic<int32_t> diff_nanoseconds;
    std::atomic<WTSyncStatus> wt_sync_status;
    std::atomic<OTMasterSyncStatus> ot_master_sync_status;
};
```

## Operational Modes

1. **Normal Mode**: Standard OT + MCU diff synchronization
2. **Gap Mode**: Enhanced accuracy with GNSS/ICB gap correction  
3. **Simulation Mode**: MFR-based testing and validation

## Recent Enhancements

- **Multi-Sync System Clock**: 5-second threshold for stable updates
- **UDP Time Channel**: Network-based time synchronization
- **Enhanced Gap Mode**: Dual-source timing for automotive navigation

## Communication Protocols

- **SOME/IP**: Primary automotive protocol for MCU communication
- **UDP**: Lightweight network protocol (*********:2381)
- **HTTP**: Bootstrap time acquisition
- **Shared Memory**: High-performance IPC for time distribution