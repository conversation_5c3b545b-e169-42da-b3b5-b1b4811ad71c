#!/bin/bash

LIB_DIR="build/deploy/common"

STRIP_TOOL="aarch64-unknown-nto-qnx7.1.0-strip"

if [ ! -d "$LIB_DIR" ]; then
    echo "folder $LIB_DIR not exist!"
    exit 1
fi

find "$LIB_DIR" \( -name "*.so" -o -name "*.a" \) -exec $STRIP_TOOL {} \;

\cp -f scripts/*_time_manager.sh build/deploy
\cp -f scripts/*.json build/deploy
\cp -f scripts/*.cfg build/deploy

echo "all files have been striped."

make deploy