#!/bin/bash

##
#0. scp C/C++ solib from IPD to local directory: [~/crash/solib]
#        libc.so.4 / libc++.so.1.0
#1. download CI tar image file;
#2. run gen_gdbcmd.sh with CI tar image file, then it will untar image and generate gdbcmd file in current directory;
#3. modify the gdb cmd file, select 'file' program and 'core' coredump file;
#3. source qnxsdp-env.sh
#4. ntoaarch64-gdb -x $gdbcmd_file

dirname=""
filename=""
solibpath=""

help(){
	script_name=$(echo $0 | awk -F '/' '{print $NF}')
	echo "$script_name: untar CI_TAR_Package into current dir and generate gdb cmd file."
    echo "Err: Please run $script_name with CI_TAR_Package."
    echo "ex: $script_name ~/Downloads/maf2.2.1_SW030_RP12_Deliver_1209-ec039c97-SW030_RP12_V3.1.14-127685-20211214073057-all.tar"
}

get_name(){
    if [ ! -f $1 ];then
		echo "Err: $1 not exist!"
		exit 1
	fi

	#dirname=$(echo $1 | grep -P "SW\d+_RP\d+_V.*-all" -o | awk -F '-all' '{print $1}')
	#dirname=$(echo $1 | grep -P "SW\d+_RP\S+_V.*-all" -o | awk -F '-all' '{print $1}')
	dirname=$(echo $1 | awk -F '/' '{print $NF}')
	dirname=${dirname%.tgz}
	filename="$dirname.gdbcmd"
	echo "will untar to dir: $dirname"
	echo "will gen cmdfile : $filename"
	echo
	echo

	if [ -d $dirname ]; then
		echo "dirname:[$dirname] have exist! exit...!"
		exit 1
	fi

	if [ -f $filename ]; then
		echo "filename:[$filename] have exist! exit...!"
		exit 1
	fi
}

untar_ci_package(){
  echo "untaring $1 ..."

  rm -rf $dirname
  mkdir -p $dirname
  tar -xvf $1 -C $dirname

  #for file in `ls $dirname/*.tar`
  #  do
  #    tar -xvf $file -C $dirname
  #done
  #rm $dirname/*.tar
}

search_so_path(){
    solibpath=$(find $PWD/$dirname -name "*.so*" | sed 's/\/lib[^\/]\{1,\}[^\/]//g' | grep -v Linux | awk '{print $1":\\\n"}' | sort -u | awk 'NF>0 {print}')
	echo "\nsolibpath = $solibpath"
}

generate_gdbcmd(){
	packname=$(echo $1 | awk -F '/' '{print $NF}')
	echo "## $packname" > $filename
	echo >> $filename
	echo >> $filename

	echo "## Please modify below to choose the right file." >> $filename

	#program=$(find $PWD/$dirname -name mfrlaunch | grep QNX-aarch64-gcc5.4)
	program=$(find $PWD/$dirname -name mfrlaunch | grep bin)
	echo "file $program" >> $filename
	echo >> $filename
	echo >> $filename

	echo "## Please config below to set coredump file." >> $filename
	echo "core coredump.core" >> $filename
	echo >> $filename
	echo >> $filename

	echo "set pagination off" >> $filename
	echo "set print pretty"   >> $filename
	echo >> $filename
	echo >> $filename

	echo "## Please scp C/C++ librarys from IPD to ~/crash/solib directory." >> $filename
	echo "set solib-search-path \\" >> $filename
	echo "#~/crash/solib:\\" >> $filename
	echo "#~/crash/nvlib:\\" >> $filename
	echo "#~/crash/nvlib/aquantia/tools/flashtools/3.10.0:\\" >> $filename
	echo "#~/crash/nvlib/nvsipl_drv:\\" >> $filename
	echo "#~/crash/nvlib/screen:\\" >> $filename
	echo "$solibpath" >> $filename
	echo >> $filename
}

## main enter
if [ ! $1 ];then
	help
	exit 1
fi

get_name $1
untar_ci_package $1
search_so_path $1
generate_gdbcmd $1
