trigger:
  branches:
    include:
      - main
      - dev

  tags:
    include:
      - '*'

variables:
  package-name: obf_time_manager
  project-name: $(System.Teamproject)
  repo-name: $(Build.Repository.Name)
  scan_allowlist_file: mtime_core_dev.csv

parameters: 
  - name: enable_release_devcar
    displayName: enable release obf_time_manager for DevCar
    type: boolean
    default: true
  - name: enable_release_8620_qnx
    displayName: enable release obf_time_manager for refcar 8620
    type: boolean
    default: true

resources: 
  containers:
  - container: inform
    endpoint: artifactory
    image: artifactory.momenta.works/docker-msd/sonarqube-inform:v0.0.1
    options: -i -t
  - container: scanner
    endpoint: artifactory
    image: artifactory.momenta.works/docker-msd/sonar-scanner-client:20220802-v2.8
  - container: ci
    endpoint: artifactory
    image: artifactory.momenta.works/docker-msd/mlogci:v1.3-20211226
    options: -i -t
  - container: parasoft
    endpoint: artifactory
    image: artifactory.momenta.works/docker-momenta/build_env_devcar_momenta_cmake_with_cuda_11.1_parasoft:v0.0.21_2
    options: -i -t -e BUILD_ENV_ID=devcar_with_cuda11.1 -e BENV_ID=devcar_with_cuda11.1 -e _MODO_DOCKER_TAG=artifactory.momenta.works/docker-momenta/build_env_devcar_momenta_cmake_with_cuda_11.1:v0.0.4
  - container: mf_system_ci
    image: artifactory.momenta.works/docker-momenta/mf_system_ci:v2
    options: -i --rm -v /var/run/docker.sock:/var/run/docker.sock -v /usr/bin/docker:/usr/bin/docker -v /tmp/mf_system/:/tmp/mf_system/ -v /root/.conan/:/root/.conan/ -v /tmp/work/:/tmp/work/
  - container: mf_system_devcar
    image: artifactory.momenta.works/docker-momenta/build_env_devcar_momenta_cmake_with_cuda_11.1:v0.0.15
    options: -i -t --rm -e MFS_SYSTEM_CFG_YAML=config/Devcar/system3.0.yaml


  repositories:
    - repository: templates
      type: git
      name: qa/m-static-code-check
      ref: refs/heads/master_mfs
    - repository: mf_system
      type: git
      name: maf/mf_system
      ref: refs/heads/tm/dev
 
stages: 
- stage: format
  jobs:
  - job: format
    workspace:
      clean: all
    container: ci
    pool:
      name: 'default'
    displayName: 'format'
    steps:
      - checkout: self
        clean: true
        lfs: true
        submodules: recursive
        persistCredentials: true
 
      - bash: |
          echo "pwd is $(pwd)"
          ls -lh
          ls -lh .ci/format.py
          ls -lh $(Build.SourcesDirectory)
          cat .ci/format.py
          pwd
          cd $(Build.SourcesDirectory)
          echo "build source dir is $(Build.SourcesDirectory)"
          python3 .ci/format.py check
        displayName: 'execute_format'
        failOnStderr: true
    condition: ne(variables['Build.Reason'], 'Schedule')

- template: pipelines.staticcheck-notification.yml@templates
- template: parasoft.yml@templates

- stage: build
  dependsOn: 'format'
  jobs:
  - job: build_devcar
    container: mf_system_devcar
    displayName: 'mf_system_ci (devcar)'
    condition: and(succeeded(), or(${{ parameters.enable_release_devcar }}, startsWith(variables['build.sourceBranch'], 'refs/tags/')))

    steps:
      - checkout: self
        clean: true
        lfs: true
        submodules: recursive
        persistCredentials: true
      - checkout: mf_system 
      - bash: |
          set -e
          export MF_SYSTEM_ROOT_DIR=$PWD/mf_system
          cd mf_system
          source mfs_setup.bash
          export | grep -i MF_SYSTEM_ROOT_DIR
          make install
          cd ../$(Build.Repository.Name)
          make prepare build project
        displayName: 'execute_compile mf_system_devcar'

      - task: PublishBuildArtifacts@1
        inputs:
          PathtoPublish: '$(Build.SourcesDirectory)/time_manager/build/deploy.tar.gz'
          ArtifactName: 'deploy'
          publishLocation: 'Container'

- stage: test
  dependsOn: 'build'
  condition: and(succeeded(), ne(variables['Build.Reason'], 'Schedule'))
  variables:
    deploy_path: "$(Build.SourcesDirectory)/time_manager/deploy"
    package_path: $(deploy_path)/modules/$(package-name)

  jobs:

  - job: unittest
    container: mf_system_devcar
    displayName: 'unittest'
    workspace:
      clean: all
    pool:
      name: 'default'

    steps:
      - checkout: self

      - task: DownloadBuildArtifacts@0
        inputs: 
          artifactName: 'deploy'
          downloadType: 'single'
          downloadPath: '$(Build.SourcesDirectory)/time_manager'

      - bash: |
          echo "tree pwd"
          tree $(pwd)
          echo "deploy_path=$(deploy_path)"
          echo "package_path=$(package_path)"
          echo "pwd=$(pwd)"
          echo "=====>  do unzip!"
          cd $(deploy_path)
          echo "pwd=$(pwd)"
          tree $(deploy_path)
          tar -xvmf $(deploy_path)/deploy.tar.gz --strip-components 1
          echo "=====>  finish unzip!"
          echo
          
          echo "pwd=$(pwd)"
          tree $(deploy_path)
          chmod o+x -R  $(package_path)/test
          echo "=====>  run obf_time_manager cpp unittest!"
          export LD_LIBRARY_PATH=`pwd`/common/lib:$LD_LIBRARY_PATH
          echo "LD LIBRARY PATH=$LD_LIBRARY_PATH"
          export LD_LIBRARY_PATH=/root/.iso_compiler/v2/gcc/x86_64-9.3/lib64/:$LD_LIBRARY_PATH
          echo "begin to run test pwd is $(pwd)"
          echo "begin to run ut, and who am i $(who) and $(w) and $(whoami)"
          $(package_path)/test/time_manager_unit_test 2>&1
          if [ $? -ne 0 ];then 
            echo "=====>  time manager unittest failed"
            exit 1
          else
            echo "=====>  time manager unittest succeeded"
          fi

        displayName: 'do cpp unittest'

- stage: gcov_coverage
  dependsOn: [test, build]
  condition: and(succeeded(), ne(variables['Build.Reason'], 'Schedule'))
  variables:
    deploy_path: "$(Build.SourcesDirectory)/time_manager/deploy"
    package_path: $(deploy_path)/modules/$(package-name)

  jobs:
  - job: gcov_coverage
    container: mf_system_devcar
    displayName: "gcov_coverage"
    workspace:
      clean: all
    pool:
      name: 'default'

    steps:
      - checkout: self
        clean: true
        lfs: true
        submodules: recursive
        persistCredentials: true
      - checkout: mf_system 

      - task: DownloadBuildArtifacts@0
        inputs: 
          artifactName: 'deploy'
          downloadType: 'single'
          downloadPath: '$(Build.SourcesDirectory)/time_manager'

      - bash: |
          set -e
          set -x
          ls
          cd mf_system
          source mfs_setup.bash
          export | grep -i MF_SYSTEM_ROOT_DIR
          make install

          echo "deploy_path=$(deploy_path)"
          echo "package_path=$(package_path)"
          echo "pwd=$(pwd)"
          echo "=====>  do unzip!"
          cd $(deploy_path)
          echo "pwd=$(pwd)"
          tree $(deploy_path)
          tar -xvmf $(deploy_path)/deploy.tar.gz --strip-components 1
          echo "=====>  finish unzip!"
          echo       

          echo "pwd=$(pwd)"
          chmod o+x -R  $(package_path)/test
          echo "=====>  run obf_time_manager cppapi test!"

          cd ../../$(Build.Repository.Name)
          pwd
          command -v lcov || apt-get -y install lcov
          sed -i 's/set(CMAKE_BUILD_TYPE RelWithDebInfo)//g' CMakeLists.txt
          sed -i 's/${CMAKE_CXX_FLAGS} -Wall -Werror/${CMAKE_CXX_FLAGS} -Wall -Werror -g -O0 -fprofile-arcs -ftest-coverage/g' CMakeLists.txt
          sed -i 's/${CMAKE_C_FLAGS} -Wall -Werror/${CMAKE_C_FLAGS} -Wall -Werror -g -O0 -fprofile-arcs -ftest-coverage/g' CMakeLists.txt
          
          make prepare
          make build

          cd build
          ls
          lcov --directory . --zerocounters
          export LD_LIBRARY_PATH=/root/.iso_compiler/v2/gcc/x86_64-9.3/lib64/:$LD_LIBRARY_PATH
          ls
          echo "LD_LIBRARY_PATH is ${LD_LIBRARY_PATH}"
          export LD_LIBRARY_PATH=deploy/common/lib:$LD_LIBRARY_PATH
          echo "after LD_LIBRARY_PATH is ${LD_LIBRARY_PATH}"
          ./bin/time_manager_unit_test
          ls
          lcov --rc lcov_branch_coverage=1 --directory . --capture --output-file coverage.info
          #sed -i -e 's/,-1$/,0/g' coverage.info
          echo "build sorces director is $(Build.SourcesDirectory)"
          lcov --remove coverage.info '$(Build.SourcesDirectory)/time_manager/thirdparty/*' '$(Build.SourcesDirectory)/time_manager/test/*' 'c++/*' 'gtest/*' '/root/.conan/*' '/root/.iso_compiler/*' '5.4.0/*' '/usr/include/*' --rc lcov_branch_coverage=1 -o coverage_filter.info

          genhtml --branch-coverage --highlight --legend --output-directory $(Build.SourcesDirectory)/coverage coverage_filter.info
          lcov --list coverage_filter.info
          ls ./
          ls $(Build.SourcesDirectory)
          echo "pwd is $(pwd)"
          
        displayName: 'generate coverage report'

      - task: PublishBuildArtifacts@1
        inputs:
          PathtoPublish: '$(Build.SourcesDirectory)/coverage'
          ArtifactName: '$(Agent.JobName)_$(Build.SourceBranch)_coverage_html_report'
          publishLocation: 'Container'

      - task: PublishBuildArtifacts@1
        inputs:
          PathtoPublish: '$(Build.SourcesDirectory)/time_manager/build/coverage.info'
          ArtifactName: '$(Agent.JobName)_$(Build.SourceBranch)_coverage_info_file'
          publishLocation: 'Container'

      - task: PublishCodeCoverageResults@1
        inputs:
          summaryFileLocation: $(Build.SourcesDirectory)/time_manager/coverage/index-sort-f.html