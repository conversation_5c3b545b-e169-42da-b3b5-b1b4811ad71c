cmake_minimum_required(VERSION 3.5)

project(TimeManager)

set(TIME_MANAGER_VERSION 1.0)

set(IS_ROOT OFF)
if (${CMAKE_CURRENT_SOURCE_DIR} STREQUAL ${CMAKE_SOURCE_DIR})
  set(IS_ROOT ON)
endif()

set(TIME_MANAGER_CURRENT_VERSION "1.0")
add_definitions(-DTM_VERSION="1.0")

execute_process(
    COMMAND git rev-parse HEAD
    WORKING_DIRECTORY ${CMAKE_SOURCE_DIR}
    OUTPUT_VARIABLE GIT_VERSION
    ERROR_VARIABLE GIT_ERROR
    RESULT_VARIABLE GIT_RESULT
    OUTPUT_STRIP_TRAILING_WHITESPACE
)

if(NOT GIT_RESULT EQUAL 0)
    message(WARNING "Failed to get Git version information: ${GIT_ERROR}. Setting TimeManager Git version to unknown.")
    set(GIT_VERSION "unknown")
else()
    message(STATUS "TimeManager Git version: ${GIT_VERSION}")
endif()

add_definitions("-DTM_GIT_VERSION=\"${GIT_VERSION}\"")

# ====================================================

option(TIME_MANAGER_ENABLE_TEST "option for whether enable build test" ${IS_ROOT})

option(TIME_MANAGER_ENABLE_SAMPLE "option for whether enable build sample" ${IS_ROOT})

option(ENABLE_CONAN_BUILD "option for whether build project by conan" OFF)

option(ENABLE_SUBMODULE_BUILD "option for whether build project by submodule" OFF)

option(USE_SYSTEM_TIME "Use system time function" OFF)

option(USE_TIME_SYNC "Use time synchronization function" OFF)

option(USE_MQTT "Use MQTT communication channel" OFF)

option(USE_MQTT "Use MQTT communication channel" OFF)

# ====================================================

# platform settings, compile isolation
if (USE_TIME_SYNC)
  add_definitions(-DTM_USE_TIME_SYNC=ON)
endif()

if (USE_SYSTEM_TIME)
  add_definitions(-DTM_USE_SYSTEM_TIME=ON)
endif()

if (USE_TIME_GAP)
  add_definitions(-DTM_USE_TIME_GAP=ON)
endif()

if (USE_DM)
  add_definitions(-DTM_USE_DM=ON)
endif()

# ====================================================

if (NOT CMAKE_BUILD_TYPE)
  message(STATUS "CMAKE_BUILD_TYPE not set, default Debug")
  set(CMAKE_BUILD_TYPE Debug)
elseif (CMAKE_BUILD_TYPE MATCHES "Release")  
  set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fvisibility-inlines-hidden")
endif()

set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Werror")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wall -Werror")

# ====================================================

set(CMAKE_CXX_STANDARD 14)

include_directories(include)

if (${CMAKE_SYSTEM_NAME} STREQUAL "QNX")
else()
endif()

if(ENABLE_CONAN_BUILD)
  message(STATUS "----------------- TimeManager build by conan ----------------")
  include(.cmake/depend/conan.cmake)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)
elseif(ENABLE_SUBMODULE_BUILD)
  message(STATUS "----------------- TimeManager build by submodule -----------------")
endif()

# TODO: maybe changed latter
if(ENABLE_SUBMODULE_BUILD)
add_subdirectory(thirdparty/googletest EXCLUDE_FROM_ALL)
endif()

# ====================================================
set(CMAKE_INSTALL_PREFIX ${CMAKE_CURRENT_BINARY_DIR}/install/)

install(DIRECTORY ${PROJECT_SOURCE_DIR}/include/
  DESTINATION include
  FILES_MATCHING PATTERN "*.h"
)
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin)

add_executable(time_manager)

set(tm_sources
${CMAKE_SOURCE_DIR}/src/time_manager.cpp
${CMAKE_SOURCE_DIR}/src/common/time_manager_config.cpp
${CMAKE_SOURCE_DIR}/src/monitor/heartbeat_monitor.cpp
${CMAKE_SOURCE_DIR}/src/monitor/running_mode_manager.cpp
${CMAKE_SOURCE_DIR}/src/communication/mfr_simulation_node.cpp
${CMAKE_SOURCE_DIR}/src/communication/mfr_simulation_time_channel.cpp
${CMAKE_SOURCE_DIR}/src/st/time_file_handler.cpp
)

if (USE_TIME_SYNC)
list(APPEND tm_sources ${CMAKE_SOURCE_DIR}/src/wt/wt_statuschecker.cpp)
list(APPEND tm_sources ${CMAKE_SOURCE_DIR}/src/communication/someip_time_channel.cpp)
list(APPEND tm_sources ${CMAKE_SOURCE_DIR}/src/communication/http_time_channel.cpp)
endif()

if (USE_TIME_GAP)
list(APPEND tm_sources ${CMAKE_SOURCE_DIR}/src/communication/someip_time_gap_channel.cpp)
endif()

if (USE_SYSTEM_TIME)
list(APPEND tm_sources ${CMAKE_SOURCE_DIR}/src/st/system_clock_manager.cpp)
list(APPEND tm_sources ${CMAKE_SOURCE_DIR}/src/st/system_clock_setter.cpp)
list(APPEND tm_sources ${CMAKE_SOURCE_DIR}/src/st/build_time_generator.cpp)
endif()

if (USE_DM)
list(APPEND tm_sources ${CMAKE_SOURCE_DIR}/src/monitor/dm_monitor.cpp)
list(APPEND tm_sources ${CMAKE_SOURCE_DIR}/src/monitor/did_reporter.cpp)
endif()

target_sources(time_manager PRIVATE ${tm_sources})

target_include_directories(time_manager PRIVATE src/wt src/ot src/st src/communication src/common src include/common
${CONAN_INCLUDE_DIRS_OBF_TIME_MANAGER_SDK}/common
${CONAN_INCLUDE_DIRS_OBF_TIME_MANAGER_SDK}/daemon
)

target_link_libraries(time_manager PRIVATE ${CONAN_LIBS})

if(NOT ${CMAKE_SYSTEM_NAME} STREQUAL "QNX")
  target_link_libraries(time_manager PRIVATE pthread dl rt)
endif()

install(TARGETS time_manager DESTINATION bin)

if(TIME_MANAGER_ENABLE_TEST)
  enable_testing()
  add_subdirectory(test)
endif()

add_subdirectory(tools)