#include <atomic>
#include <fcntl.h>
#include <sys/mman.h>
#include <cerrno>
#include <stdio.h>
#include <string.h>
#include <unistd.h>

const size_t PAGE_SIZE = 4096;
struct wtinfo {
  std::atomic<int32_t> diff_seconds; // Difference in seconds.
  std::atomic<int32_t>
      diff_nanoseconds; // Difference in nanoseconds for higher precision.
  std::atomic<uint8_t> wt_sync_status; // Current synchronization status of WT.
  std::atomic<uint8_t>
      ot_master_sync_status; // Current synchronization status of the OT master.
};

// 简化的数据结构
struct TestData {
    char front_guard[PAGE_SIZE];
    
    alignas(PAGE_SIZE) struct {
        wtinfo info;
        std::atomic<bool> is_initialized;
        char padding[PAGE_SIZE - sizeof(std::atomic<bool>)];
    } data_page;
    
    char back_guard[PAGE_SIZE];
};

int main() {
    const char* SHM_NAME = "/test_atomic";
    fprintf(stderr, "Starting test...\n");

    // 1. 创建共享内存
    int fd = shm_open(SHM_NAME, O_CREAT | O_RDWR, 0666);
    if (fd == -1) {
        fprintf(stderr, "shm_open failed: %s\n", strerror(errno));
        return 1;
    }
    fprintf(stderr, "shm_open success, fd: %d\n", fd);

    // 2. 设置大小
    if (ftruncate(fd, sizeof(TestData)) == -1) {
        fprintf(stderr, "ftruncate failed: %s\n", strerror(errno));
        close(fd);
        return 1;
    }
    fprintf(stderr, "ftruncate success, size: %zu\n", sizeof(TestData));

    // 3. 映射内存
    fprintf(stderr, "Attempting mmap...\n");
    void* raw_ptr = mmap(nullptr, sizeof(TestData), 
                        PROT_READ | PROT_WRITE, MAP_SHARED, fd, 0);
    if (raw_ptr == MAP_FAILED) {
        fprintf(stderr, "mmap failed: %s\n", strerror(errno));
        close(fd);
        return 1;
    }
    TestData* data = static_cast<TestData*>(raw_ptr);
    fprintf(stderr, "mmap success, addr: %p\n", (void*)data);

    // 4. 设置保护页
    fprintf(stderr, "Setting protection pages...\n");
    if (mprotect(&data->front_guard, PAGE_SIZE, PROT_NONE) != 0) {
        fprintf(stderr, "Front guard mprotect failed: %s\n", strerror(errno));
        munmap(data, sizeof(TestData));
        close(fd);
        return 1;
    }
    if (mprotect(&data->back_guard, PAGE_SIZE, PROT_NONE) != 0) {
        fprintf(stderr, "Back guard mprotect failed: %s\n", strerror(errno));
        munmap(data, sizeof(TestData));
        close(fd);
        return 1;
    }
    fprintf(stderr, "Protection pages set successfully\n");

    // 5. 测试原子操作
    fprintf(stderr, "Testing atomic operations...\n");
    fprintf(stderr, "data_page address: %p\n", (void*)&data->data_page);
    fprintf(stderr, "is_initialized address: %p\n", (void*)&data->data_page.is_initialized);

    // 尝试 CAS 操作
    bool expected = false;
    fprintf(stderr, "Attempting compare_exchange_strong...\n");
    if (data->data_page.is_initialized.compare_exchange_strong(expected, true)) {
        fprintf(stderr, "First initialization success\n");
    } else {
        fprintf(stderr, "Already initialized (expected was: %d)\n", expected);
    }

    // 6. 清理
    fprintf(stderr, "Cleaning up...\n");
    munmap(data, sizeof(TestData));
    close(fd);
    shm_unlink(SHM_NAME);
    fprintf(stderr, "Test completed\n");

    return 0;
}
