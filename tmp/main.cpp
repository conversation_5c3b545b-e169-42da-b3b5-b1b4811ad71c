#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <sys/mman.h>
#include <unistd.h>
#include <string.h>
#include <errno.h>
#include <atomic>
#include <stdint.h>

const size_t PAGE_SIZE = 4096;
const char* SHM_NAME = "/ts_tm";

enum WTSyncStatus : uint8_t {
   WT_STATE_INITIALIZED,
   WT_STATE_TIMEOUT,
   WT_STATE_JUMPED,
   WT_STATE_SYNCHRONIZED,
   WT_STATE_FROM_HTTP,
   WT_STATE_COUNT
};

enum OTMasterSyncStatus : uint8_t {
   OT_MASTER_NOT_SYNCHRONIZED,
   OT_MASTER_SYNC_UNLOCKED,
   OT_MASTER_SYNC_LOCKED
};

enum RunningMode : uint8_t {
   TM_NORMAL_MODE,
   TM_SIMULATION_MODE,
   TM_MODE_COUNT
};

enum SimluationTimeMode : uint8_t {
   TM_SIMULATION_UNINITIALIZED,
   TM_SIMULATION_RELATIVE_TIME_MODE,
   TM_SIMULATION_ABSOLUTE_TIME_MODE,
   TM_SIMULATION_TIME_DIFF_MODE,
   TM_SIMULATION_TIME_MODE_COUNT
};

struct WTInfo {
   std::atomic<int32_t> diff_seconds;
   std::atomic<int32_t> diff_nanoseconds;
   std::atomic<WTSyncStatus> wt_sync_status;
   std::atomic<OTMasterSyncStatus> ot_master_sync_status;
};

struct WTSharedData {
   char front_guard_page[PAGE_SIZE];
   alignas(PAGE_SIZE) struct {
       WTInfo info;
       std::atomic<bool> is_initialized{false};
       std::atomic<RunningMode> running_mode{TM_NORMAL_MODE};
       std::atomic<SimluationTimeMode> simulation_time_mode{TM_SIMULATION_UNINITIALIZED};
       std::atomic<std::uint64_t> heartbeat_timestamp{0};
       std::atomic<std::int64_t> simulation_timestamp{0};
   } data_page;
   char back_guard_page[PAGE_SIZE];
};

const char* get_wt_sync_status_str(WTSyncStatus status) {
   switch(status) {
       case WT_STATE_INITIALIZED: return "INITIALIZED";
       case WT_STATE_TIMEOUT: return "TIMEOUT";
       case WT_STATE_JUMPED: return "JUMPED";
       case WT_STATE_SYNCHRONIZED: return "SYNCHRONIZED";
       case WT_STATE_FROM_HTTP: return "FROM_HTTP";
       default: return "UNKNOWN";
   }
}

const char* get_ot_master_sync_status_str(OTMasterSyncStatus status) {
   switch(status) {
       case OT_MASTER_NOT_SYNCHRONIZED: return "NOT_SYNCHRONIZED";
       case OT_MASTER_SYNC_UNLOCKED: return "SYNC_UNLOCKED";
       case OT_MASTER_SYNC_LOCKED: return "SYNC_LOCKED";
       default: return "UNKNOWN";
   }
}

const char* get_running_mode_str(RunningMode mode) {
   switch(mode) {
       case TM_NORMAL_MODE: return "NORMAL";
       case TM_SIMULATION_MODE: return "SIMULATION";
       default: return "UNKNOWN";
   }
}

const char* get_simulation_time_mode_str(SimluationTimeMode mode) {
   switch(mode) {
       case TM_SIMULATION_UNINITIALIZED: return "UNINITIALIZED";
       case TM_SIMULATION_RELATIVE_TIME_MODE: return "RELATIVE";
       case TM_SIMULATION_ABSOLUTE_TIME_MODE: return "ABSOLUTE";
       case TM_SIMULATION_TIME_DIFF_MODE: return "DIFF";
       default: return "UNKNOWN";
   }
}

void check_page_protection(void* addr, const char* page_name) {
   // 尝试设置PROT_NONE来检查保护状态
   int ret = mprotect(addr, PAGE_SIZE, PROT_NONE);
   if (ret == 0) {
       printf("%s: Can be set to PROT_NONE (not protected)\n", page_name);
       // 恢复原来的权限
       mprotect(addr, PAGE_SIZE, PROT_READ | PROT_WRITE);
   } else {
       printf("%s: Already protected (PROT_NONE) - %s\n", page_name, strerror(errno));
   }
}

void check_memory_status(WTSharedData* data) {
   printf("\n=== Memory Protection Status ===\n");

   // 检查每个页面的mincore状态
   unsigned char vec[3];  // 3页
   if (mincore(data, sizeof(WTSharedData), vec) == -1) {
       printf("mincore failed: %s\n", strerror(errno));
       return;
   }

   printf("\nPage Residency Status:\n");
   printf("Front Guard Page: %s\n", (vec[0] & 1) ? "In Memory" : "Not In Memory");
   printf("Data Page: %s\n", (vec[1] & 1) ? "In Memory" : "Not In Memory");
   printf("Back Guard Page: %s\n", (vec[2] & 1) ? "In Memory" : "Not In Memory");

   printf("\nPage Protection Status:\n");
   check_page_protection(&data->front_guard_page, "Front Guard Page");
   check_page_protection(&data->data_page, "Data Page");
   check_page_protection(&data->back_guard_page, "Back Guard Page");

   printf("\nMemory Layout:\n");
   printf("Front Guard Page: %p\n", &data->front_guard_page);
   printf("Data Page: %p\n", &data->data_page);
   printf("Back Guard Page: %p\n", &data->back_guard_page);
}

void dump_shared_data(WTSharedData* data) {
   printf("\n=== WTSharedData Dump ===\n");

   printf("\nWTInfo:\n");
   printf("  diff_seconds: %d\n", data->data_page.info.diff_seconds.load());
   printf("  diff_nanoseconds: %d\n", data->data_page.info.diff_nanoseconds.load());
   printf("  wt_sync_status: %s\n",
          get_wt_sync_status_str(data->data_page.info.wt_sync_status.load()));
   printf("  ot_master_sync_status: %s\n",
          get_ot_master_sync_status_str(data->data_page.info.ot_master_sync_status.load()));

   printf("\nControl Status:\n");
   printf("  is_initialized: %s\n", data->data_page.is_initialized.load() ? "true" : "false");
   printf("  running_mode: %s\n",
          get_running_mode_str(data->data_page.running_mode.load()));
   printf("  simulation_time_mode: %s\n",
          get_simulation_time_mode_str(data->data_page.simulation_time_mode.load()));

   printf("\nTimestamps:\n");
   printf("  heartbeat_timestamp: %lu\n", data->data_page.heartbeat_timestamp.load());
   printf("  simulation_timestamp: %ld\n", data->data_page.simulation_timestamp.load());
}

int main(int argc, char* argv[]) {
   if (argc != 2) {
       printf("Usage: %s [dump|check|crash1|crash2]\n", argv[0]);
       printf("  dump   - dump shared memory content\n");
       printf("  check  - check memory protection status\n");
       printf("  crash1 - try to write to first guard page\n");
       printf("  crash2 - try to write to last guard page\n");
       return 1;
   }

   int fd = shm_open(SHM_NAME, O_RDWR, 0666);
   if (fd == -1) {
       perror("shm_open failed");
       return 1;
   }

   WTSharedData* data = (WTSharedData*)mmap(NULL, sizeof(WTSharedData),
                                           PROT_READ | PROT_WRITE,
                                           MAP_SHARED, fd, 0);
   if (data == MAP_FAILED) {
       perror("mmap failed");
       close(fd);
       return 1;
   }

   if (strcmp(argv[1], "dump") == 0) {
       dump_shared_data(data);
   }
   else if (strcmp(argv[1], "check") == 0) {
       check_memory_status(data);
   }
   else if (strcmp(argv[1], "crash1") == 0) {
       printf("Attempting to write to first guard page...\n");
       data->front_guard_page[0] = 1;
       printf("Warning: Write succeeded!\n");
   }
   else if (strcmp(argv[1], "crash2") == 0) {
       printf("Attempting to write to last guard page...\n");
       data->back_guard_page[0] = 1;
       printf("Warning: Write succeeded!\n");
   }

   munmap(data, sizeof(WTSharedData));
   close(fd);
   return 0;
}
