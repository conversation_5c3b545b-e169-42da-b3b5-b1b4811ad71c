#!/bin/bash

# UDP时间通道构建脚本

echo "==============================================="
echo "构建带UDP支持的时间管理器"
echo "==============================================="

# 检查是否在正确的目录
if [ ! -f "CMakeLists.txt" ]; then
    echo "错误：请在项目根目录运行此脚本"
    exit 1
fi

# 创建构建目录
BUILD_DIR="build"
if [ ! -d "$BUILD_DIR" ]; then
    echo "创建构建目录: $BUILD_DIR"
    mkdir -p "$BUILD_DIR"
fi

cd "$BUILD_DIR"

# 配置CMake（启用时间同步功能）
echo "配置CMake..."
cmake -DUSE_TIME_SYNC=ON -DUSE_SYSTEM_TIME=ON -DCMAKE_BUILD_TYPE=Debug ..

if [ $? -ne 0 ]; then
    echo "CMake配置失败"
    exit 1
fi

# 编译
echo "开始编译..."
make -j$(nproc)

if [ $? -ne 0 ]; then
    echo "编译失败"
    exit 1
fi

echo "==============================================="
echo "编译成功！"
echo "==============================================="

echo "生成的文件："
echo "  时间管理器: $PWD/bin/time_manager"

# 编译UDP测试客户端
echo ""
echo "编译UDP测试客户端..."
cd ..
g++ -std=c++14 -o udp_test_client tools/udp_test_client.cpp

if [ $? -eq 0 ]; then
    echo "  UDP测试客户端: $PWD/udp_test_client"
else
    echo "UDP测试客户端编译失败"
fi

echo ""
echo "使用方法："
echo "1. 运行时间管理器："
echo "   ./build/bin/time_manager -v"
echo ""
echo "2. 发送测试UDP包："
echo "   ./udp_test_client -h 173.2.1.1 -p 2381"
echo ""
echo "查看详细说明："
echo "   cat UDP_TIME_CHANNEL_README.md" 