#pragma once

#include "service/TimeManager/TimeManagerService_proxy.h"
#include "system_clock_manager.h"
#include "wt_statuschecker.h"
#include <atomic>

namespace tmgr {

class SomeIPTimeChannel {
public:
  SomeIPTimeChannel(std::shared_ptr<WTStatusChecker> wt_status_checker,
                    std::shared_ptr<SystemClockManager> system_clock_manager);
  ~SomeIPTimeChannel();
  bool init();

private:
  // NOTE: proxy constructor is synchronous, where will be a block point when
  // proxy create is waiting
  obf::cm::proxy::TimeManager_TimeManagerService::TimeManagerService_Proxy
      proxy_{};
  obf::cm::TimeSyncStruct time_sync_struct;
  std::shared_ptr<WTStatusChecker> wt_status_checker_;
  std::shared_ptr<SystemClockManager> system_clock_manager_;
  std::atomic<bool> is_thread_active_{false};
  std::atomic<bool> is_need_update_system_time{true};
  static int32_t last_diff_seconds;
  static int32_t last_diff_nanoseconds;
};
} // namespace tmgr
