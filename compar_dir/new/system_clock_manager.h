#pragma once

#include "common/timer.hpp"
#include "data_types.h"
#include "system_clock_setter.h"
#include "time_file_handler.h"
#include <cstdint>

namespace tmgr {

class SystemClockManager {
public:
  SystemClockManager(const std::string &filePath, int diskWriteIntervalMillSec);
  ~SystemClockManager();

  // TODO: just check first flag
  // TODO: need update with someip logic for refresh system clock
  bool updateSystemClockFirstTimeWithWTInfo(int64_t diff_in_nanoseconds);
  void updateSystemClockRegularWithWTInfo(int64_t diff_in_nanoseconds);
  static bool isTimePointFutureThanSystemClock(const Timestamp &timePoint);

private:
  void setSystemClock(const Timestamp &timePoint);
  void startDiskWriteTimer();
  void initSystemTime();
  void checkOTTimeValid();
  void checkWTTimeValid(int64_t nano_count);

private:
  TimeFileHandler timeFileHandler_;
  std::string filePath_;
  int diskWriteIntervalMillSec_;
  Timer diskWriteTimer_;
  SystemClockSetter systemTimeSetter_;
  bool needFirstTimeUpdateSystemTime_{
      true}; // this flag can be toggle to meet different requirement
  static constexpr int64_t kUpdateThresholdS = 5; // 5s
};

} // namespace tmgr