#include "someip_time_channel.h"
#include "someip_data_coverter.hpp"
#include "tmlog.h"
#include "wt_serializer.h"

namespace tmgr {

int32_t SomeIPTimeChannel::last_diff_seconds = 0;
int32_t SomeIPTimeChannel::last_diff_nanoseconds = 0;

SomeIPTimeChannel::SomeIPTimeChannel(
    std::shared_ptr<WTStatusChecker> wt_status_checker,
    std::shared_ptr<SystemClockManager> system_clock_manager)
    : wt_status_checker_(wt_status_checker),
      system_clock_manager_(system_clock_manager) {}

SomeIPTimeChannel::~SomeIPTimeChannel() { proxy_.TimeDiffEvent.Unsubscribe(); }

bool SomeIPTimeChannel::init() {
  proxy_.TimeDiffEvent.SetReceiveHandler([this] {
    proxy_.TimeDiffEvent.GetNewSamples(
        [this](const std::shared_ptr<obf::cm::TimeSyncStruct> sample) {
          // NOTE: choose diff according to environment variable
          static bool use_onboard_diff = []() {
            const char *env_p = std::getenv("ONBOARD_SENSORS_ENABLED");
            if (env_p != nullptr && std::string(env_p) == "0") {
              LOG_INFO("[SomeIPDataConverter]: Use Vehicle Time Diff from MCU");
              return false;
            }
            LOG_INFO("[SomeIPDataConverter]: Use Onboard Time Diff from MCU");
            return true;
          }();

          WTInfo info =
              SomeIPDataConverter::convertToWTInfo(*sample, use_onboard_diff);

          bool source_is_valid = false;

          if (use_onboard_diff) {
            source_is_valid = sample->time_diff_onbard.wt_sync_status == 0;
          } else {
            source_is_valid = sample->time_diff_vehicle.wt_sync_status == 0;
          }

          int32_t change_in_seconds = info.diff_seconds - last_diff_seconds;
          int32_t change_in_nanoseconds =
              info.diff_nanoseconds - last_diff_nanoseconds;

          last_diff_seconds = info.diff_seconds;
          last_diff_nanoseconds = info.diff_nanoseconds;

          const char *diff_source = use_onboard_diff ? "onboard" : "vehicle";

          LOG_INFO("[SomeIPTimeChannel]: receive time sync event from [%s]: "
                   "ot_syncStatus is %d, diff_seconds is %d "
                   "(%s%d), "
                   "diff_nanoseconds is %d (%s%d)",
                   diff_source, info.ot_master_sync_status.load(),
                   info.diff_seconds.load(), (change_in_seconds > 0 ? "+" : ""),
                   change_in_seconds, info.diff_nanoseconds.load(),
                   (change_in_nanoseconds > 0 ? "+" : ""),
                   change_in_nanoseconds);

          if (source_is_valid) {
            processValidTimeSource(info);
          } else {
            LOG_WARN(
                "[SomeIPTimeChannel]: this time sync event is invalid which "
                "mcu's feed wt_status is 1, ignore it");

            // even we review invalid values, but we should not treet it as
            // timeout
            wt_status_checker_->feedWTInfoDog();
          }
        });
  });

  proxy_.TimeDiffEvent.Subscribe(10);

  LOG_INFO("[SomeIPTimeChannel]: init proxy / subscribe event success");
  return true;
}

void SomeIPTimeChannel::processValidTimeSource(const WTInfo &info) {
  int64_t total_diff_nanoseconds =
      info.diff_seconds * 1000000000LL + info.diff_nanoseconds;

  auto timeGap = WTSerializer::getInstance().timeGap();
  auto wtSyncMode = WTSerializer::getInstance().wtSyncMode();

  LOG_INFO("[SomeIPTimeChannel]: wtSyncMode is %s, add "
           "total_diff_nanoseconds: %lld(ns) with gap value(normal "
           "mode will ignore it) %lld(ns) "
           "to %lld(ns)",
           wtSyncMode == WTSyncMode::WT_SYNC_MODE_NORMAL ? "Normal" : "Gap",
           total_diff_nanoseconds, timeGap, total_diff_nanoseconds + timeGap);

  if (wtSyncMode == WTSyncMode::WT_SYNC_MODE_GAP) {
    total_diff_nanoseconds += timeGap;
  }

  // make sure only one thread will be spawned to update system time
  if (is_need_first_update_system_time.load()) {
    if (system_clock_manager_->updateSystemClockFirstTimeWithWTInfo(
            total_diff_nanoseconds)) {
      is_need_first_update_system_time.store(false);
      LOG_INFO("[SomeIPTimeChannel]: ----------- update system clock "
               "first time startup finished --------- (check wt "
               "time with current system_time choose the bigger one)");
    } else {
      LOG_ERR("[SomeIPTimeChannel]: ----------- update system clock first "
              "time startup failed -----------, because "
              "cannot get ot time, maybe TimeSync is not startup, will "
              "wait for next coming message with valid ot time");
    }
  } else {
    system_clock_manager_->updateSystemClockRegularWithWTInfo(
        total_diff_nanoseconds);
  }

  wt_status_checker_->feedWTInfo(info);
}

} // namespace tmgr