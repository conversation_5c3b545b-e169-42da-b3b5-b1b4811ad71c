#include "build_time_generator.h"
#include "system_clock_utility.h"
#include <chrono>
#include <gtest/gtest.h>

TEST(BuildTimeGeneratorTest, getBuildTime) {
  auto buildTime = tmgr::BuildTimeGenerator::getBuildTime();
  std::cout << "read build time from __DATE__ and __TIME__: "
            << tmgr::SystemClockUtility::prettyStrTimePoint(buildTime)
            << std::endl;
  EXPECT_GT(buildTime.time_since_epoch().count(), 0);
  auto it = std::chrono::system_clock::time_point(std::chrono::microseconds(0));
  EXPECT_GT(buildTime.time_since_epoch(), it.time_since_epoch());
}