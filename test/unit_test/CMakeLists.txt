add_executable(time_manager_unit_test main.cpp)

set(sources 
${CMAKE_SOURCE_DIR}/src/st/system_clock_setter.cpp
${CMAKE_SOURCE_DIR}/src/st/time_file_handler.cpp
${CMAKE_SOURCE_DIR}/src/st/build_time_generator.cpp
${CMAKE_SOURCE_DIR}/tools/command_line_parser.cpp
${CMAKE_SOURCE_DIR}/src/common/time_manager_config.cpp
)

if (USE_TIME_SYNC)
list(APPEND sources ${CMAKE_SOURCE_DIR}/src/st/system_clock_manager.cpp)
endif()

if (USE_DM)
list(APPEND sources ${CMAKE_SOURCE_DIR}/src/monitor/dm_monitor.cpp)
list(APPEND sources ${CMAKE_SOURCE_DIR}/src/monitor/did_reporter.cpp)
endif()

target_sources(time_manager_unit_test PRIVATE ${sources})

set(test_sources
test_timer.cpp 
test_time_parser.cpp
test_time_file_handler.cpp
test_system_clock_setter.cpp
test_build_time_generator.cpp
test_command_line_parser.cpp
test_platform_sizeof.cpp
)

if (USE_TIME_SYNC)
list(APPEND test_sources test_system_clock_manager.cpp)
endif()

target_sources(time_manager_unit_test PRIVATE ${test_sources})

target_link_libraries(time_manager_unit_test PRIVATE gtest ${CONAN_LIBS})

if(NOT ${CMAKE_SYSTEM_NAME} STREQUAL "QNX")
  target_link_libraries(time_manager_unit_test PRIVATE pthread rt)
endif()

target_include_directories(time_manager_unit_test PRIVATE ${CMAKE_SOURCE_DIR}/src/common
${CMAKE_SOURCE_DIR}/src
${CMAKE_SOURCE_DIR}/include/common
${CMAKE_SOURCE_DIR}/src/st
${CMAKE_SOURCE_DIR}/src/ot
${CMAKE_SOURCE_DIR}/tools
${CMAKE_SOURCE_DIR}/src/wt
${CONAN_INCLUDE_DIRS_OBF_TIME_MANAGER_SDK}/common
${CONAN_INCLUDE_DIRS_OBF_TIME_MANAGER_SDK}/daemon
)

add_test(NAME unittest COMMAND time_manager_unit_test)

install(TARGETS time_manager_unit_test DESTINATION test)
