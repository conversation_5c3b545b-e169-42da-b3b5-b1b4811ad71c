#include "data_types.h"
#include "system_clock_utility.h"
#include "time_file_handler.h"
#include "gtest/gtest.h"
#include <chrono>
#include <ratio>

using namespace tmgr;

TEST(TimeFileHandlerTest, createTimestamp) {
  TimeFileHandler timeFileHandler("test.txt");
  Timestamp st = Timestamp(std::chrono::nanoseconds(12345));
  timeFileHandler.writeTime(st);
  sleep(1);
  auto res_st = timeFileHandler.readTime();
  EXPECT_EQ(st, res_st);
  EXPECT_EQ(st.time_since_epoch().count(), res_st.time_since_epoch().count());

  TimeFileHandler timeFileHandler2("test.txt");
  Timestamp st2 = Timestamp(std::chrono::nanoseconds(45678));
  timeFileHandler2.writeTime(st2);
  sleep(1);
  auto res_st2 = timeFileHandler2.readTime();
  EXPECT_EQ(st2, res_st2);
  EXPECT_EQ(st2.time_since_epoch().count(), res_st2.time_since_epoch().count());

  auto it_hold = std::chrono::system_clock::now();
  {
    TimeFileHandler timeFileHandler("test.txt");
    auto tm = SystemClockUtility::convertSystemClockUsToTimestampNs(it_hold);
    timeFileHandler.writeTime(tm);
    sleep(1);
    auto res = timeFileHandler.readTime();
    auto res_s = SystemClockUtility::convertTimestampNsToSystemClockUs(res);
    EXPECT_EQ(it_hold, res_s);
  }

  {
    TimeFileHandler timeFileHandler("test.txt");
    auto it = timeFileHandler.readTime();
    auto ts = SystemClockUtility::convertTimestampNsToSystemClockUs(it);
    EXPECT_EQ(ts, it_hold);
  }
}