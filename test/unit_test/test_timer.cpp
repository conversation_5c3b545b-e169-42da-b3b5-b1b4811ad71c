#include "timer.hpp"
#include "gtest/gtest.h"
#include <condition_variable>
#include <mutex>
#include <unistd.h>

TEST(TimerTest, simple_timer) {
  Timer t;
  int a = 0;
  t.start(80, [&a]() { a++; });
  sleep(1);
  EXPECT_GT(a, 10);
  int b = a;
  t.stop();
  EXPECT_GE(a, b);
}

TEST(TimerTest, CallbackIsCalledAfterInterval) {
  Timer timer;
  int called = 0;
  std::mutex mtx;
  std::condition_variable cv;
  bool finished = false;

  timer.start(100, [&]() {
    std::lock_guard<std::mutex> lk(mtx);
    ++called;
    finished = true;
    cv.notify_one();
  });

  std::unique_lock<std::mutex> lk(mtx);
  cv.wait(lk, [&] { return finished; });

  timer.stop();

  EXPECT_EQ(called, 1);
}

TEST(TimerTest, TimerCanBeStopped) {
  Timer timer;
  int called = 0;

  timer.start(50, [&]() { ++called; });

  std::this_thread::sleep_for(
      std::chrono::milliseconds(120)); // Wait a bit more than two intervals
  timer.stop();

  EXPECT_EQ(called, 2); // Depending on timing, might be called twice
}

TEST(TimerTest, RestartTimerWorks) {
  Timer timer;
  int called = 0;

  timer.start(50, [&]() { ++called; });

  std::this_thread::sleep_for(std::chrono::milliseconds(70));
  timer.stop();
  EXPECT_GE(called, 1);

  called = 0; // Reset called count

  timer.start(50, [&]() { ++called; });

  std::this_thread::sleep_for(std::chrono::milliseconds(70));
  timer.stop();

  EXPECT_GE(called,
            1); // Ensure timer was restarted and callback was called again
}