#include "data_types.h"
#include "system_clock_manager.h"
#include <chrono>
#include <gtest/gtest.h>

using namespace tmgr;

// Helper function to create a Timestamp from nanoseconds
Timestamp createTimestampFromNanoseconds(int64_t nanoseconds) {
  return Timestamp(std::chrono::nanoseconds(nanoseconds));
}

// Test case for the current system time
TEST(SystemClockManagerTest, CurrentTime) {
  auto now = std::chrono::system_clock::now();
  Timestamp currentSystemTime = createTimestampFromNanoseconds(
      std::chrono::duration_cast<std::chrono::nanoseconds>(
          now.time_since_epoch())
          .count());
  EXPECT_FALSE(
      SystemClockManager::isTimePointFutureThanSystemClock(currentSystemTime));

  Timestamp currSys =
      Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
          now.time_since_epoch()));
  EXPECT_EQ(currSys, currentSystemTime);
  EXPECT_EQ(currSys.time_since_epoch().count(),
            std::chrono::duration_cast<std::chrono::nanoseconds>(
                now.time_since_epoch())
                .count());
}

// Test case for a future time
TEST(SystemClockManagerTest, FutureTime) {
  auto now = std::chrono::system_clock::now();
  Timestamp futureTime = createTimestampFromNanoseconds(
      std::chrono::duration_cast<std::chrono::nanoseconds>(
          now.time_since_epoch())
          .count() +
      1000000000 // +1 second
  );
  EXPECT_TRUE(SystemClockManager::isTimePointFutureThanSystemClock(futureTime));

  std::chrono::system_clock::time_point now_new =
      now + std::chrono::seconds(10);

  Timestamp fTimestamp =
      Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
          now_new.time_since_epoch()));

  EXPECT_TRUE(SystemClockManager::isTimePointFutureThanSystemClock(fTimestamp));
}

// Test case for a past time
TEST(SystemClockManagerTest, PastTime) {
  auto now = std::chrono::system_clock::now();
  Timestamp pastTime = createTimestampFromNanoseconds(
      std::chrono::duration_cast<std::chrono::nanoseconds>(
          now.time_since_epoch())
          .count() -
      1000000000 // -1 second
  );
  EXPECT_FALSE(SystemClockManager::isTimePointFutureThanSystemClock(pastTime));

  auto now_old = now - std::chrono::seconds(10);

  Timestamp pTimestamp =
      Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
          now_old.time_since_epoch()));

  EXPECT_FALSE(
      SystemClockManager::isTimePointFutureThanSystemClock(pTimestamp));
}