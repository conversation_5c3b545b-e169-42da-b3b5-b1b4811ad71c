//#include "data_types.h"
//#include "system_clock_setter.h"
//#include "system_clock_utility.h"
//#include "time_file_handler.h"
//#include <chrono>
//#include <gtest/gtest.h>
//
// using namespace tmgr;
//
// TEST(SystemClockSetterTest, setSystemTime) {
//  Timestamp emptyT;
//  Timestamp zeroT(std::chrono::nanoseconds(0));
//  EXPECT_EQ(emptyT, zeroT);
//
//  Timestamp eeT{};
//  Timestamp randomT;
//  EXPECT_EQ(eeT, randomT);
//  EXPECT_EQ(eeT, zeroT);
//
//  auto current_t = std::chrono::system_clock::now();
//  auto current_tm =
//      SystemClockUtility::convertSystemClockUsToTimestampNs(current_t);
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(current_tm));
//}
//
// TEST(SystemClockSetterTest, setSystemTimeWithinTime) {
//  auto current_t = std::chrono::system_clock::now();
//  auto current_orig = current_t;
//  auto current_tm =
//      SystemClockUtility::convertSystemClockUsToTimestampNs(current_t);
//  current_tm += std::chrono::seconds(5);
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(current_tm));
//  auto current_t_again = std::chrono::system_clock::now();
//  auto comp = std::chrono::duration_cast<std::chrono::seconds>(current_t_again
//  -
//                                                               current_t);
//  std::cout << "comp count is " << comp.count() << std::endl;
//  EXPECT_TRUE(comp.count() >= 5 && comp.count() <= 10);
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(
//      SystemClockUtility::convertSystemClockUsToTimestampNs(current_orig)));
//}
//
// TEST(SystemClockSetterTest, SetSystemTimeToPast) {
//  auto current_t = std::chrono::system_clock::now();
//  auto current_tm =
//      SystemClockUtility::convertSystemClockUsToTimestampNs(current_t);
//  current_tm -= std::chrono::seconds(5);
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(current_tm));
//  auto new_t = std::chrono::system_clock::now();
//  auto diff =
//      std::chrono::duration_cast<std::chrono::seconds>(new_t - current_t);
//  std::cout << "++++++++++++++++++++diff count() is " << diff.count()
//            << std::endl;
//  EXPECT_TRUE(diff.count() >= -5);
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(
//      SystemClockUtility::convertSystemClockUsToTimestampNs(current_t)));
//}
//
// TEST(SystemClockSetterTest, SetSystemTimeAndRevertImmediately) {
//  auto original_time = std::chrono::system_clock::now();
//  auto original_tm =
//      SystemClockUtility::convertSystemClockUsToTimestampNs(original_time);
//  auto new_time = original_time + std::chrono::seconds(5);
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(
//      SystemClockUtility::convertSystemClockUsToTimestampNs(new_time)));
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(original_tm));
//  auto current_time = std::chrono::system_clock::now();
//  auto diff = std::chrono::duration_cast<std::chrono::seconds>(current_time -
//                                                               original_time);
//  EXPECT_TRUE(std::abs(diff.count()) < 1);
//}
//
// TEST(SystemClockSetterTest, SetSystemTimeToFutureAndVerify) {
//  auto current_t = std::chrono::system_clock::now();
//  auto future_tm =
//      SystemClockUtility::convertSystemClockUsToTimestampNs(current_t) +
//      std::chrono::seconds(10);
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(future_tm));
//  auto adjusted_t = std::chrono::system_clock::now();
//  auto diff =
//      std::chrono::duration_cast<std::chrono::seconds>(adjusted_t -
//      current_t);
//  EXPECT_GE(diff.count(), 10);
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(
//      SystemClockUtility::convertSystemClockUsToTimestampNs(current_t)));
//}
//
//// WARN: should turn off when going into ci/cd
// TEST(SystemClockSetterTest, SetSupperBigTime) {
//  auto current_t = std::chrono::system_clock::now();
//  auto future_tm =
//      SystemClockUtility::convertSystemClockUsToTimestampNs(current_t) +
//      std::chrono::seconds(999999999);
//
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(future_tm));
//  sleep(5);
//  // reset
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(
//      SystemClockUtility::convertSystemClockUsToTimestampNs(
//          current_t + std::chrono::seconds(6))));
//  sleep(5);
//}
//
////// WARN: should turn off when going into ci/cd
// TEST(SystemClockSetterTest, SetSupperBigTimeFromFile) {
//  auto now = std::chrono::system_clock::now();
//  auto future_tm = SystemClockUtility::convertSystemClockUsToTimestampNs(now)
//  +
//                   std::chrono::seconds(999999999);
//  TimeFileHandler timeFileHandler("just-try.txt");
//  timeFileHandler.writeTime(future_tm);
//  auto res = timeFileHandler.readTime();
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(res));
//  auto now2 = std::chrono::system_clock::now();
//  auto diff = std::chrono::duration_cast<std::chrono::seconds>(now2 - now);
//  EXPECT_TRUE(diff.count() > 99999);
//  sleep(5);
//  // reset
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(
//      SystemClockUtility::convertSystemClockUsToTimestampNs(now) +
//      std::chrono::seconds(6)));
//}
//
//// WARN: should turn off when going into ci/cd
// TEST(SystemClockSetterTest, SetTimeStampTime) {
//  sleep(5);
//  auto now = std::chrono::system_clock::now();
//
//  Timestamp tm = Timestamp(std::chrono::seconds(888888888));
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(tm));
//  auto now2 = std::chrono::system_clock::now();
//  auto diff = std::chrono::duration_cast<std::chrono::seconds>(now2 - now);
//  EXPECT_TRUE(diff.count() < 888888);
//  sleep(5);
//  // reset
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(now +
//  std::chrono::seconds(7)));
//
//  sleep(5);
//
//  auto current_sys = std::chrono::system_clock::now();
//  auto current_sys_timestamp =
//      SystemClockUtility::convertSystemClockUsToTimestampNs(current_sys);
//  EXPECT_TRUE(
//      SystemClockSetter::setSystemTime(current_sys +
//      std::chrono::seconds(20)));
//  sleep(5);
//  auto current_sys_tmp =
//  SystemClockUtility::convertSystemClockUsToTimestampNs(
//                             std::chrono::system_clock::now()) -
//                         std::chrono::seconds(20);
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(current_sys_tmp));
//  auto current_sys2 = std::chrono::system_clock::now();
//  auto current_sys2_timestamp =
//      SystemClockUtility::convertSystemClockUsToTimestampNs(current_sys2);
//  auto duff_dif = std::chrono::duration_cast<std::chrono::seconds>(
//      current_sys2 - current_sys);
//  auto duff_dif_tm = std::chrono::duration_cast<std::chrono::seconds>(
//      current_sys2_timestamp - current_sys_timestamp);
//
//  std::cout << "////////////////////duff_dif count is " << duff_dif.count()
//            << std::endl;
//  EXPECT_TRUE(duff_dif.count() >= 5 && duff_dif.count() <= 6);
//  EXPECT_TRUE(duff_dif_tm.count() >= 5 && duff_dif_tm.count() <= 6);
//}
//
//// WARN: should turn off when going into ci/cd
// TEST(SystemClockSetterTest, SetWrongTime) {
//  auto it = std::chrono::system_clock::now();
//  Timestamp origi_t = SystemClockUtility::convertSystemClockUsToTimestampNs(
//      std::chrono::system_clock::now());
//  Timestamp timestamp =
//      Timestamp(std::chrono::system_clock::now().time_since_epoch());
//  EXPECT_NE(origi_t, timestamp);
//  EXPECT_NE(origi_t.time_since_epoch().count(),
//            timestamp.time_since_epoch().count());
//
//  origi_t += std::chrono::seconds(999999);
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(origi_t));
//  sleep(5);
//  origi_t = Timestamp(std::chrono::nanoseconds(0));
//  EXPECT_FALSE(SystemClockSetter::setSystemTime(origi_t));
//  sleep(5);
//  // reset
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(it +
//  std::chrono::seconds(11))); it = std::chrono::system_clock::now(); Timestamp
//  wit = Timestamp(std::chrono::nanoseconds(-100000));
//  EXPECT_FALSE(SystemClockSetter::setSystemTime(wit));
//  // reset
//  sleep(5);
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(it + std::chrono::seconds(6)));
//}
//
//// WARN: should turn off when going into ci/cd
// TEST(SystemClockSetterTest, checkTimestampAndSystemClock) {
//  auto current_sc = std::chrono::system_clock::now();
//  auto current_sc_timestamp =
//      SystemClockUtility::convertSystemClockUsToTimestampNs(current_sc);
//  auto current_sc_timestamp_sc =
//      SystemClockUtility::convertTimestampNsToSystemClockUs(
//          current_sc_timestamp);
//  EXPECT_EQ(current_sc, current_sc_timestamp_sc);
//  EXPECT_EQ(current_sc.time_since_epoch().count(),
//            current_sc_timestamp_sc.time_since_epoch().count());
//
//  auto sc_precision = std::chrono::system_clock::duration::period();
//
//  if (sc_precision.den == 1000000) {
//    EXPECT_EQ(current_sc.time_since_epoch().count(),
//              current_sc_timestamp.time_since_epoch().count() / 1000);
//  } else {
//    EXPECT_EQ(current_sc.time_since_epoch().count(),
//              current_sc_timestamp.time_since_epoch().count());
//  }
//
//  auto current_sc_plus_5_seconds = current_sc + std::chrono::seconds(5);
//  auto current_sc_timestamp_plus_5_seconds =
//      current_sc_timestamp + std::chrono::seconds(5);
//  EXPECT_EQ(current_sc_plus_5_seconds.time_since_epoch(),
//            current_sc_timestamp_plus_5_seconds.time_since_epoch());
//
//  if (sc_precision.den == 1000000) {
//    EXPECT_EQ(current_sc_plus_5_seconds.time_since_epoch().count() * 1000,
//              current_sc_timestamp_plus_5_seconds.time_since_epoch().count());
//  } else {
//    EXPECT_EQ(current_sc_plus_5_seconds.time_since_epoch().count(),
//              current_sc_timestamp_plus_5_seconds.time_since_epoch().count());
//  }
//
//  EXPECT_TRUE(
//      SystemClockSetter::setSystemTime(current_sc_timestamp_plus_5_seconds));
//  auto after_5_seconds_timestamp =
//      SystemClockUtility::convertSystemClockUsToTimestampNs(
//          std::chrono::system_clock::now());
//  EXPECT_NE(after_5_seconds_timestamp, current_sc_timestamp_plus_5_seconds);
//
//  auto diff_dur = after_5_seconds_timestamp.time_since_epoch() -
//                  current_sc.time_since_epoch();
//  EXPECT_NE(diff_dur.count(), 5000000000);
//
//  // reset
//  EXPECT_TRUE(SystemClockSetter::setSystemTime(
//      current_sc_timestamp_plus_5_seconds - std::chrono::seconds(3)));
//
//  EXPECT_EQ((current_sc_timestamp +
//  std::chrono::seconds(3)).time_since_epoch(),
//            (current_sc.time_since_epoch() + std::chrono::seconds(3)));
//  if (sc_precision.den == 1000000) {
//    EXPECT_EQ(
//        (current_sc_timestamp + std::chrono::seconds(3))
//            .time_since_epoch()
//            .count(),
//        (current_sc.time_since_epoch() + std::chrono::seconds(3)).count() *
//            1000);
//  }
//  EXPECT_EQ((current_sc + std::chrono::seconds(12)).time_since_epoch(),
//            current_sc_timestamp.time_since_epoch() +
//            std::chrono::seconds(12));
//  if (sc_precision.den == 1000000) {
//    EXPECT_EQ(
//        (current_sc + std::chrono::seconds(12)).time_since_epoch().count() *
//            1000,
//        (current_sc_timestamp.time_since_epoch() + std::chrono::seconds(12))
//            .count());
//  }
//  EXPECT_EQ((current_sc + std::chrono::seconds(7)).time_since_epoch(),
//            (current_sc_timestamp + std::chrono::milliseconds(7000))
//                .time_since_epoch());
//  if (sc_precision.den == 1000000) {
//    EXPECT_EQ(
//        (current_sc + std::chrono::seconds(7)).time_since_epoch().count() *
//            1000,
//        (current_sc_timestamp + std::chrono::milliseconds(7000))
//            .time_since_epoch()
//            .count());
//  }
//}