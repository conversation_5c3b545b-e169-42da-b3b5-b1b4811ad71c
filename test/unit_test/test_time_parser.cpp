#include "time_parser.hpp"
#include "gtest/gtest.h"
#include <chrono>
#include <unistd.h>

using namespace tmgr;

TEST(TimeParser, ParsesValidTimeCorrectly) {
  std::string timeStr = "2023-02-25 20:30:00";
  std::string format = "%Y-%m-%d %H:%M:%S";
  auto parsedTime = TimeParser::ParseFromFormattedString(timeStr, format);

  std::tm tm = {};
  std::istringstream ss(timeStr);
  ss >> std::get_time(&tm, format.c_str());
  auto expectedTimeT = std::mktime(&tm);
  auto expectedTimePoint =
      std::chrono::system_clock::from_time_t(expectedTimeT);

  EXPECT_EQ(parsedTime, expectedTimePoint);
}

TEST(TimeParser, ReturnsCurrentTimeOnFormatError) {
  std::string timeStr = "invalid-format";
  std::string format = "%Y-%m-%d %H:%M:%S";
  auto parsedTime = TimeParser::ParseFromFormattedString(timeStr, format);
  auto now = std::chrono::system_clock::now();

  auto duration =
      std::chrono::duration_cast<std::chrono::seconds>(parsedTime - now)
          .count();
  EXPECT_TRUE(duration >= -1 && duration <= 1);
}

TEST(TimeParser, ReturnsCurrentTimeOnInvalidTime) {
  std::string timeStr = "2023-13-32 25:61:00"; // 显然是无效的时间
  std::string format = "%Y-%m-%d %H:%M:%S";
  auto parsedTime = TimeParser::ParseFromFormattedString(timeStr, format);
  auto now = std::chrono::system_clock::now();

  auto duration =
      std::chrono::duration_cast<std::chrono::seconds>(parsedTime - now)
          .count();
  EXPECT_TRUE(duration >= -1 && duration <= 1);
}

TEST(TimeFormatter, FormatsTimePointCorrectly) {
  std::tm tm = {};
  std::istringstream ss("2023-03-25 20:30:00");
  ss >> std::get_time(&tm, "%Y-%m-%d %H:%M:%S");
  auto expectedTimeT = std::mktime(&tm);
  auto timePoint = std::chrono::system_clock::from_time_t(expectedTimeT);

  std::time_t timeT = std::chrono::system_clock::to_time_t(timePoint);
  std::tm *timePtr = std::localtime(&timeT);
  std::ostringstream oss;
  oss << std::put_time(timePtr, "%Y-%m-%d %H:%M:%S");
  std::string formattedTime = oss.str();

  EXPECT_EQ(formattedTime, "2023-03-25 20:30:00");
}
