#include "command_line_parser.h"
#include <cstddef>
#include <gtest/gtest.h>

using namespace tmgr;

TEST(CommandLineParserTest, NoOptionsOrFlags) {
  int argc = 1;
  char *argv[] = {(char *)"./a.out"};
  CommandLineParser parser(argc, argv);
  EXPECT_FALSE(parser.isValid());
}

TEST(CommandLineParserTest, SingleFlag) {
  int argc = 2;
  char *argv[] = {(char *)"./a.out", (char *)"-f"};
  CommandLineParser parser(argc, argv);
  EXPECT_TRUE(parser.isValid());
  EXPECT_TRUE(parser.hasFlag("-f"));
  EXPECT_FALSE(parser.hasOption("-f"));
}

TEST(CommandLineParserTest, SingleOptionWithValue) {
  int argc = 3;
  char *argv[] = {(char *)"./a.out", (char *)"--option", (char *)"value"};
  CommandLineParser parser(argc, argv);
  EXPECT_TRUE(parser.isValid());
  EXPECT_TRUE(parser.hasOption("--option"));
  EXPECT_EQ(parser.getOption("--option"), "value");
}

TEST(CommandLineParserTest, InvalidArgument) {
  {
    int argc = 2;
    char *argv[] = {(char *)"./a.out", (char *)"invalid"};
    CommandLineParser parser(argc, argv);
    EXPECT_TRUE(parser.isValid());
  }

  {
    int argc = 4;
    char *argv[] = {(char *)"./a.out", (char *)"-f", (char *)"value",
                    (char *)"value"};
    CommandLineParser parser(argc, argv);
    EXPECT_TRUE(parser.isValid());
  }
}

// 测试多个选项和标志的组合
TEST(CommandLineParserTest, MultipleOptionsAndFlags) {
  {
    int argc = 6;
    char *argv[] = {(char *)"./a.out", (char *)"-f",        (char *)"--option1",
                    (char *)"value1",  (char *)"--option2", (char *)"value2"};
    CommandLineParser parser(argc, argv);
    EXPECT_TRUE(parser.isValid());
    EXPECT_TRUE(parser.hasFlag("-f"));
    EXPECT_TRUE(parser.hasOption("--option1"));
    EXPECT_TRUE(parser.hasOption("--option2"));
    EXPECT_EQ(parser.getOption("--option1"), "value1");
    EXPECT_EQ(parser.getOption("--option2"), "value2");
  }
  {
    int argc = 6;
    char *argv[] = {(char *)"./a.out", (char *)"--option1", (char *)"value13",
                    (char *)"-f",      (char *)"--option2", (char *)"value23"};
    CommandLineParser parser(argc, argv);
    EXPECT_TRUE(parser.isValid());
    EXPECT_TRUE(parser.hasFlag("-f"));
    EXPECT_TRUE(parser.hasOption("--option1"));
    EXPECT_TRUE(parser.hasOption("--option2"));
    EXPECT_EQ(parser.getOption("--option1"), "value13");
    EXPECT_EQ(parser.getOption("--option2"), "value23");
  }
  {
    int argc = 6;
    char *argv[] = {(char *)"./a.out", (char *)"--option1", (char *)"value13",
                    (char *)"-f",      (char *)"--option2", (char *)"value23"};
    CommandLineParser parser(argc, argv);
    EXPECT_TRUE(parser.isValid());
    auto options_map = parser.getOptions();
    auto flags_set = parser.getFlags();
    EXPECT_EQ(options_map.size(), (const long unsigned int)2);
    EXPECT_EQ(flags_set.size(), (const long unsigned int)1);
    EXPECT_EQ(options_map.at("--option1"), "value13");
    EXPECT_EQ(options_map.at("--option2"), "value23");
    EXPECT_EQ(flags_set.count("-f"), (size_t)1);
  }
}

TEST(CommandLineParserTest, MultipleOptionsAndFlagsCommands) {
  {
    int argc = 8;
    char *argv[] = {(char *)"./a.out",   (char *)"time",   (char *)"-f",
                    (char *)"--option1", (char *)"value1", (char *)"--option2",
                    (char *)"value2",    (char *)"wait"};
    CommandLineParser parser(argc, argv);
    EXPECT_TRUE(parser.isValid());
    EXPECT_TRUE(parser.hasFlag("-f"));
    EXPECT_TRUE(parser.hasOption("--option1"));
    EXPECT_TRUE(parser.hasOption("--option2"));
    EXPECT_EQ(parser.getOption("--option1"), "value1");
    EXPECT_EQ(parser.getOption("--option2"), "value2");
    std::vector<std::string> rves = {"time", "wait"};
    EXPECT_EQ(parser.getCommands(), rves);
  }
  {
    int argc = 7;
    char *argv[] = {(char *)"./a.out",   (char *)"time",   (char *)"-f",
                    (char *)"--option1", (char *)"value1", (char *)"--option2",
                    (char *)"value2"};
    CommandLineParser parser(argc, argv);
    EXPECT_TRUE(parser.isValid());
    EXPECT_TRUE(parser.hasFlag("-f"));
    EXPECT_TRUE(parser.hasOption("--option1"));
    EXPECT_TRUE(parser.hasOption("--option2"));
    EXPECT_EQ(parser.getOption("--option1"), "value1");
    EXPECT_EQ(parser.getOption("--option2"), "value2");
    EXPECT_EQ(parser.getCommands(), std::vector<std::string>{"time"});
  }
  {
    int argc = 7;
    char *argv[] = {(char *)"./a.out", (char *)"-f",   (char *)"--option1",
                    (char *)"value1",  (char *)"date", (char *)"--option2",
                    (char *)"value2"};
    CommandLineParser parser(argc, argv);
    EXPECT_TRUE(parser.isValid());
    EXPECT_TRUE(parser.hasFlag("-f"));
    EXPECT_TRUE(parser.hasOption("--option1"));
    EXPECT_TRUE(parser.hasOption("--option2"));
    EXPECT_EQ(parser.getCommands(), std::vector<std::string>{"date"});
    EXPECT_EQ(parser.getOption("--option1"), "value1");
    EXPECT_EQ(parser.getOption("--option2"), "value2");
  }
  {
    int argc = 10;
    char *argv[] = {(char *)"./a.out", (char *)"-f",        (char *)"1",
                    (char *)"yes",     (char *)"--option1", (char *)"value1",
                    (char *)"date",    (char *)"--option2", (char *)"value2",
                    (char *)"no"};
    CommandLineParser parser(argc, argv);
    EXPECT_TRUE(parser.isValid());
    EXPECT_FALSE(parser.hasFlag("-f"));
    EXPECT_TRUE(parser.hasOption("--option1"));
    EXPECT_TRUE(parser.hasOption("--option2"));
    EXPECT_TRUE(parser.hasOption("-f"));
    EXPECT_EQ(parser.getOption("-f"), "1");
    EXPECT_EQ(parser.getCommands(),
              std::vector<std::string>({"yes", "date", "no"}));
    EXPECT_EQ(parser.getOption("--option1"), "value1");
    EXPECT_EQ(parser.getOption("--option2"), "value2");
  }
  {
    int argc = 2;
    char *argv[] = {(char *)"./a.out", (char *)"link"};
    CommandLineParser parser(argc, argv);
    EXPECT_TRUE(parser.isValid());
    EXPECT_FALSE(parser.hasFlag("-f"));
    EXPECT_FALSE(parser.hasOption("--option1"));
    EXPECT_FALSE(parser.hasOption("--option2"));
    EXPECT_EQ(parser.getCommands(), std::vector<std::string>{"link"});
  }
}