#include <chrono>
#include <iostream>

using namespace std;

int main() {
  std::cout
      << "period num is "
      << std::chrono::high_resolution_clock::time_point::duration::period::num
      << std::endl;
  std::cout
      << "period den is "
      << std::chrono::high_resolution_clock::time_point::duration::period::den
      << std::endl;

  std::chrono::system_clock::time_point st = std::chrono::system_clock::now();
  std::cout << "system clock now dump " << st.time_since_epoch().count()
            << std::endl;
  auto iis = std::chrono::system_clock::from_time_t(0);
  (void)iis;

  std::chrono::high_resolution_clock::time_point ht =
      std::chrono::high_resolution_clock::now();
  std::cout << "high resolution now dmp " << ht.time_since_epoch().count()
            << std::endl;

  // std::chrono::system_clock::time_point p =
  // std::chrono::high_resolution_clock::now(); std::cout <<
  // p.time_since_epoch().count() << std::endl;

  // 转换为time_t，这样可以使用ctime等函数
  std::time_t st_time = std::chrono::system_clock::to_time_t(st);

  // 将time_t转换为可读的字符串形式
  // 注意：std::ctime可能在多线程环境中不安全，具体取决于实现
  std::string timeStr = std::ctime(&st_time);

  // 移除末尾的换行符，因为std::ctime的输出以换行符结束
  if (!timeStr.empty() && timeStr.back() == '\n') {
    timeStr.pop_back();
  }

  // 使用LOG_INFO或其他日志机制打印时间
  cout << "st time is " << timeStr << endl;

  std::chrono::system_clock::time_point tp =
      std::chrono::system_clock::time_point(
          std::chrono::duration_cast<std::chrono::system_clock::duration>(
              ht.time_since_epoch()));

  std::time_t tp_time = std::chrono::system_clock::to_time_t(tp);
  std::string tp_time_str = std::ctime(&tp_time);
  //    // 移除末尾的换行符，因为std::ctime的输出以换行符结束
  //    if (!htimeStr.empty() && htimeStr.back() == '\n') {
  //      htimeStr.pop_back();
  //    }
  //
  //    // 使用LOG_INFO或其他日志机制打印时间
  cout << "ht time is " << tp_time_str;

  //        // 转换为time_t，这样可以使用ctime等函数
  //    std::time_t ht_time = std::chrono::high_resolution_clock::to_time_t(ht);
  //
  //    // 将time_t转换为可读的字符串形式
  //    // 注意：std::ctime可能在多线程环境中不安全，具体取决于实现
  //    std::string htimeStr = std::ctime(&ht_time);
  //
  //    // 移除末尾的换行符，因为std::ctime的输出以换行符结束
  //    if (!htimeStr.empty() && htimeStr.back() == '\n') {
  //      htimeStr.pop_back();
  //    }
  //
  //    // 使用LOG_INFO或其他日志机制打印时间
  //    cout << "ht time is " << htimeStr;

  auto current = std::chrono::high_resolution_clock::now();
  auto current_st = std::chrono::system_clock::time_point(
      std::chrono::duration_cast<std::chrono::system_clock::duration>(
          current.time_since_epoch()));
  auto current_time = std::chrono::system_clock::to_time_t(current_st);
  std::string current_time_str = std::ctime(&current_time);
  cout << "last current time str is " << current_time_str;

  std::chrono::system_clock::from_time_t(current_time);
  // std::chrono::high_resolution_clock::from_time_t(current_time);

  return 0;
}