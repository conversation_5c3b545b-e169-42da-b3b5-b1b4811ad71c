//#include <fcntl.h> // For O_RDONLY
//#include <unistd.h> // For read(), close()
//#include <iostream>
//#include <vector>
//#include <cstring> // For strerror()
//#include <errno.h> // For errno
//
// int main() {
//    int fd = open("/dev/shmem/ptp", O_RDONLY);
//    if (fd == -1) {
//        std::cerr << "open /dev/shmem/ptp fail: " << strerror(errno) <<
//        std::endl; return 1;
//    }
//
//    const size_t bufferSize = 1024;
//    std::vector<char> buffer(bufferSize);
//
//    ssize_t bytesRead = read(fd, buffer.data(), bufferSize);
//    if (bytesRead == -1) {
//        std::cerr << "/dev/shmem/ptp read fail: " << strerror(errno) <<
//        std::endl; close(fd); return 1;
//    }
//
//    std::cout << "success read " << bytesRead << " bytes data" << std::endl;
//
//    std::cout << "data: ";
//    for(int i = 0; i < bytesRead && i < 10; ++i) {
//        std::cout << buffer[i];
//    }
//    std::cout << std::endl;
//
//    close(fd);
//
//    return 0;
//}
//

#include <iostream>
#include <vector>

using namespace std;

struct Student {

  // implement all the operatoris that comiple generatue automcatically with
  // cout

  Student() { std::cout << "in con\n"; }

  Student(const Student &s) { std::cout << "in copy\n"; }

  Student(Student &&s) { std::cout << "in move\n"; }

  Student &operator=(const Student &s) {
    std::cout << "in copy=\n";
    return *this;
  }

  Student &operator=(Student &&s) {
    std::cout << "in move operator=\n";
    return *this;
  }

  bool operator==(const Student &s) {
    std::cout << "in it\n";
    return true;
  }
};

int main(int argc, char **argv) {
  cout << "in new " << endl;
  auto v = vector<Student>();
  v.push_back(Student());
  v.emplace_back();
  return 0;
}
