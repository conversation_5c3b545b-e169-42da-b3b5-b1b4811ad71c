#include <chrono>
#include <cstdio>
#include <cstring>
#include <iostream>
#include <sys/time.h> // Include the header file for 'settimeofday' function

#include <chrono>
#include <iostream>
template <typename Clock> void print_clock_precision() {
  typedef typename Clock::period P; // 时钟的周期类型
  if (std::ratio_less_equal<P, std::nano>::value) {
    // 如果周期小于或等于1纳秒
    std::cout << std::fixed << double(P::num) / P::den
              << " 秒（精度至少为纳秒）" << std::endl;
  } else if (std::ratio_less_equal<P, std::micro>::value) {
    // 如果周期小于或等于1微秒
    std::cout << std::fixed << double(P::num) / P::den
              << " 秒（精度至少为微秒）" << std::endl;
  } else if (std::ratio_less_equal<P, std::milli>::value) {
    // 如果周期小于或等于1毫秒
    std::cout << std::fixed << double(P::num) / P::den << " 秒（精度为毫秒）"
              << std::endl;
  } else {
    // 周期大于1毫秒
    std::cout << "精度低于毫秒" << std::endl;
  }
}

using namespace std;

int main() {
  cout << "steady clock is          "
       << std::chrono::steady_clock::now().time_since_epoch().count()
       << " clock req is " << std::chrono::steady_clock::period::den << " // "
       << std::chrono::steady_clock::period::num << endl;

  cout << "high resolution clock is "
       << std::chrono::high_resolution_clock::now().time_since_epoch().count()
       << endl;

  cout << "system clock is          "
       << std::chrono::system_clock::now().time_since_epoch().count() << endl;

  // set system clock to epoch 0

  //    std::chrono::system_clock::time_point tp =
  //        std::chrono::system_clock::time_point();

  std::chrono::system_clock::time_point tp = std::chrono::system_clock::now();

  cout << "null constructor tp count is " << tp.time_since_epoch().count()
       << endl;

  // set current system clock to tp
  struct timeval tv;
  auto duration = tp.time_since_epoch();
  auto seconds =
      std::chrono::duration_cast<std::chrono::seconds>(duration).count();

  tv.tv_sec = seconds + 8000000;
  tv.tv_usec = std::chrono::duration_cast<std::chrono::microseconds>(
                   duration % std::chrono::seconds(1))
                   .count() +
               80000;

  cout << "dump tv:" << endl;
  cout << "tv_sec is " << tv.tv_sec << endl;
  cout << "tv_usec is " << tv.tv_usec << endl;

  cout << "set current time to zero " << endl;
  auto res = settimeofday(&tv, nullptr);
  cout << "res is " << res << " error reason is " << strerror(errno) << endl;

  cout << "redump these clocks:" << endl;

  cout << "steady clock is          "
       << std::chrono::steady_clock::now().time_since_epoch().count() << endl;

  cout << "high resolution clock is "
       << std::chrono::high_resolution_clock::now().time_since_epoch().count()
       << endl;

  cout << "system clock is          "
       << std::chrono::system_clock::now().time_since_epoch().count() << endl;

  std::cout << "system_clock 分辨率: ";
  print_clock_precision<std::chrono::system_clock>();

  std::cout << "steady_clock 分辨率: ";
  print_clock_precision<std::chrono::steady_clock>();

  std::cout << "high_resolution_clock 分辨率: ";
  print_clock_precision<std::chrono::high_resolution_clock>();

  return 0;
}