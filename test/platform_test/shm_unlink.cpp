#include <cerrno>  // For errno
#include <cstring> // For strerror
#include <fcntl.h> // For O_* constants
#include <iostream>
#include <sys/mman.h> // For shared memory
#include <unistd.h>   // For ftruncate, close

int main() {

  const char *shm_name = "/my_shared_memory";
  const size_t shm_size = 1024; // 分配1KB的共享内存

  // Step 1: 创建共享内存对象
  int shm_fd = shm_open(shm_name, O_CREAT | O_RDWR, 0666);
  if (shm_fd == -1) {
    std::cerr << "shm_open failed: " << strerror(errno) << std::endl;
    return 1;
  }

  // Step 2: 设置共享内存大小
  if (ftruncate(shm_fd, shm_size) == -1) {
    std::cerr << "ftruncate failed: " << strerror(errno) << std::endl;
    close(shm_fd);
    return 1;
  }

  // 此时可以初始化内存、读写数据等（示例中省略了这些步骤）
  std::cout << "use shm_unlink create success sleep..." << std::endl;
  sleep(10);

  // Step 3: 删除（解除链接）共享内存对象
  // 注意：即使在解除链接后，只要文件描述符没有关闭，当前进程仍然可以使用共享内存
  if (shm_unlink(shm_name) == -1) {
    std::cerr << "shm_unlink failed: " << strerror(errno) << std::endl;
    close(shm_fd);
    return 1;
  }

  std::cout << "use shm_unlink enter sleep..." << std::endl;
  sleep(10);

  // 使用完毕后关闭文件描述符
  if (close(shm_fd) == -1) {
    std::cerr << "close failed: " << strerror(errno) << std::endl;
    return 1;
  }

  std::cout << "Shared memory \"" << shm_name
            << "\" successfully created, used, and removed." << std::endl;

  return 0;
}
