#include <chrono>
#include <iostream>
using namespace std;
using namespace std::chrono;
int main() {

  // 也可以直接创建一个以秒为单位的 time_point
  auto specific_time_point = system_clock::time_point(seconds(10));

  // auto specific_time_point = system_clock::time_point(seconds(10));

  std::cout << "Specific time point 10 seconds since epoch: "
            << specific_time_point.time_since_epoch().count() << "\n";

  auto t = time_point_cast<milliseconds>(specific_time_point);
  std::cout << "after millseconds converted" << t.time_since_epoch().count()
            << "\n";

  time_point<system_clock, milliseconds> t2 =
      time_point_cast<milliseconds>(specific_time_point);
  std::cout << "after time_point_casst converted"
            << t2.time_since_epoch().count() << "\n";

  return 0;
}