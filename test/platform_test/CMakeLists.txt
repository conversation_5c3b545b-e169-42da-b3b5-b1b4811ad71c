add_executable(high_resolution_system_clock_test high_resolution_system_clock.cpp)
add_executable(tm_time_test tm_time.cpp)
add_executable(system_clock_test system_clock.cpp)
add_executable(build_time_test build_time.cpp)
add_executable(shm_unlink_test shm_unlink.cpp)
if(NOT ${CMAKE_SYSTEM_NAME} STREQUAL "QNX")
    target_link_libraries(shm_unlink_test rt)
endif()
add_executable(steady_clock_test steady_clock.cpp)
add_executable(shared_memory_open shared_memory_open.cpp)
if (USE_TIME_SYNC)
add_executable(http_client http_client.cpp)
target_include_directories(http_client PRIVATE ${CONAN_INCLUDE_DIRS_CPP_HTTPLIB})
target_link_libraries(http_client socket)
endif()
add_executable(system_clock_precision system_clock_precision.cpp)