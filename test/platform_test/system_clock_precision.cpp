#include <chrono>
#include <cxxabi.h>
#include <iostream>
#include <memory>
#include <type_traits>
#include <typeinfo>

std::string demangle(const char *name) {
  int status = -1;
  std::unique_ptr<char, void (*)(void *)> res{
      abi::__cxa_demangle(name, nullptr, nullptr, &status), std::free};
  return (status == 0) ? res.get() : name;
}

template <typename Clock> void print_clock_info() {
  using namespace std::chrono;

  using period = typename Clock::duration::period;
  using rep = typename Clock::duration::rep;

  std::cout << "Period num: " << period::num << ", den: " << period::den
            << std::endl;
  std::cout << "Rep type: " << demangle(typeid(rep).name()) << std::endl;

  if (std::is_same<period, seconds::period>::value) {
    std::cout << "Time unit is seconds.\n";
  } else if (std::is_same<period, milliseconds::period>::value) {
    std::cout << "Time unit is milliseconds.\n";
  } else if (std::is_same<period, microseconds::period>::value) {
    std::cout << "Time unit is microseconds.\n";
  } else if (std::is_same<period, nanoseconds::period>::value) {
    std::cout << "Time unit is nanoseconds.\n";
  } else {
    std::cout << "Time unit is unknown.\n";
  }
}

int main() {
  using namespace std::chrono;

  std::cout << "[system_clock info]:\n";
  print_clock_info<system_clock>();

  std::cout << "\n[steady_clock info]:\n";
  print_clock_info<steady_clock>();

  std::cout << "\n[high_resolution_clock info]:\n";
  print_clock_info<high_resolution_clock>();

  return 0;
}
