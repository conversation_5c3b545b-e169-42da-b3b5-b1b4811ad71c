#include "httplib.h"
#include <chrono>
#include <ctime>
#include <iomanip>
#include <iostream>
#include <locale>
#include <sstream>

int main() {
  httplib::Client cli("www.baidu.com");

  cli.set_connection_timeout(10); // 连接超时设置为10秒
  cli.set_read_timeout(10);       // 读取超时设置为10秒
  cli.set_write_timeout(10);      // 写入超时设置为5秒

  auto res = cli.Get("/");
  if (res && res->status == 200) {
    auto date_str = res->get_header_value("Date");
    std::cout << "Date header: " << date_str << std::endl;

    std::istringstream date_stream(date_str);
    date_stream.imbue(std::locale("en_US.utf-8")); // 根据系统环境设置locale
    std::tm tm = {};
    date_stream >>
        std::get_time(&tm, "%a, %d %b %Y %H:%M:%S GMT"); // 解析日期格式

    if (date_stream.fail()) {
      std::cout << "Failed to parse Date header." << std::endl;
    } else {
      // 注意：std::mktime
      // 默认按本地时区转换，对于GMT时间，需要手动调整或使用其他方法
      auto time_c = timegm(&tm); // 使用timegm代替mktime来正确处理GMT/UTC时间
      if (time_c == static_cast<time_t>(-1)) {
        std::cout << "Conversion to time_t failed." << std::endl;
      } else {
        auto time_point = std::chrono::system_clock::from_time_t(time_c);
        auto nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(
            time_point.time_since_epoch());

        std::cout << "Nanoseconds since epoch: " << nanoseconds.count()
                  << std::endl;
      }
    }
  } else {
    std::cout << "Failed to get response or Date header missing." << std::endl;
  }

  return 0;
}
