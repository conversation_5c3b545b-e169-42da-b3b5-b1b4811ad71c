//#include <chrono>
//#include <fstream>
//#include <iostream>
//#include <stdio.h>
//#include <regex>
//
// using namespace std;
//
// struct TimeBase {
//  using rep = std::int64_t;
//  using period = std::nano;
//  using duration = std::chrono::duration<rep, period>;
//  using time_point = std::chrono::time_point<TimeBase>;
//
//  static constexpr bool is_steady = true;
//};
//
// using Timestamp = std::chrono::time_point<TimeBase,
// std::chrono::nanoseconds>;
//
// int main() {
//  Timestamp st =
//  Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
//      std::chrono::system_clock::now().time_since_epoch()));
//
//  std::ofstream file("hello_world.txt", std::ios::out | std::ios::trunc);
//  if (file.is_open()) {
//    file << "SystemClock: " << st.time_since_epoch().count();
//    printf("[TimeFileHandler]: Write system clock to file, value is %lld",
//             st.time_since_epoch().count());
//  } else {
//    printf(
//        "[TimeFileHandler]: Failed to write system clock to file, value is
//        %lld", st.time_since_epoch().count());
//  }
//
//    std::ifstream file2("hello_world.txt");
//
//  return 0;
//}

#include <chrono>
#include <ctime>
#include <fstream>
#include <iostream>
#include <regex>
#include <stdio.h>

using namespace std;

struct TimeBase {
  using rep = std::int64_t;
  using period = std::nano;
  using duration = std::chrono::duration<rep, period>;
  using time_point = std::chrono::time_point<TimeBase>;

  static constexpr bool is_steady = true;
};

using Timestamp = std::chrono::time_point<TimeBase, std::chrono::nanoseconds>;

int main() {
  //// 假设之前已经写入了时间戳到文件中
  std::ifstream file("hello_world.txt");
  // if (!file.is_open()) {
  //  cerr << "[TimeFileHandler]: Failed to open file for reading" << endl;
  //  return 1;
  //}

  std::string line;
  // std::getline(file, line);
  // file.close();

  if (file.is_open() && std::getline(file, line)) {
    printf(
        "[TimeFileHandler]: File contains a valid time format and line is %s\n",
        line.c_str());
  } else {
    printf("[TimeFileHandler]: Unable to open the file (no exist / "
           "permission issue) or file content is empty\n");
  }

  // 使用正则表达式从文件内容中提取时间戳
  std::regex regex("SystemClock: (\\d+)");
  std::smatch matches;
  if (!std::regex_search(line, matches, regex) || matches.size() < 2) {
    cerr << "[TimeFileHandler]: Failed to find timestamp in file content"
         << endl;
    return 1;
  }

  // 将提取的字符串转换为时间戳
  Timestamp::rep timestampValue = std::stoll(matches[1].str());
  Timestamp ts = Timestamp(std::chrono::nanoseconds(timestampValue));

  cout << "[TimeFileHandler]: Read system clock from file, value is "
       << timestampValue << endl;

  // 转换Timestamp到std::time_t来格式化时间
  auto duration_since_epoch = ts.time_since_epoch();
  auto sec_since_epoch =
      std::chrono::duration_cast<std::chrono::seconds>(duration_since_epoch)
          .count();
  std::time_t time_t_date = sec_since_epoch;

  // 将std::time_t转换为std::tm结构体
  std::tm *tm_date = std::localtime(&time_t_date);
  char buffer[80];
  std::strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M:%S", tm_date);

  cout << "Formatted Timestamp: " << buffer << endl;

  return 0;
}
