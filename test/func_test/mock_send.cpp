#include "mfr/core/publisher.h"
#include "mfr/mfr.h"
#include "mmemory/advanced_types.h"
#include "mtime_mfrmsgs/mtime_mfrmsgs.h"
#include "unistd.h"
#include <cstdlib>

using namespace mfr;

namespace tmgr {

struct MFRSendNodeConstants {
  static constexpr auto node_yaml = R"(
node_config:
  memory:
    total_virtual_memory_size_MB: 0
    total_shared_memory_size_MB: 0
)";

  static constexpr auto machine_yaml = R"(
log:
  level: info
  file:
    file_name: ''
    file_type: write
    max_file_size: 0
    max_file_num: 1
  enable_stderr: true
  export_frequence: 0
)";

  static constexpr auto node_type = "timemanager_send_node_type";
  static constexpr auto node_name = "timemanager_send_node_name";
  static constexpr auto machine_name = "timemanager_send_machine_name";
  static constexpr auto default_master_uri = "mfrrpc://192.168.1.1:11300";
};

} // namespace tmgr

namespace tmgr {

class MFRSimulationNode : public mfr::MFRNode {
public:
  MFRSimulationNode();
  bool on_init(mfr::MFRNodeHandle *node_handle) override;
  void on_finish() override;
  void on_running(const mfr::MFRNodeRunningInfo &info) override;

private:
  mfr::MFRPublisher *publisher_clock{nullptr};
  mfr::MFRPublisher *publisher_expect{nullptr};
  mfr::MFRPublisher *publisher_diff{nullptr};
};

MFRSimulationNode::MFRSimulationNode() {
  fprintf(stderr, "con con con con con\n");
}

bool MFRSimulationNode::on_init(mfr::MFRNodeHandle *node_handle) {
  node_handle->trigger_manager().set_time_trigger(1);

  { // get publisher
    MFRPublisherConfig pub_config{};
    pub_config.topic_name = "/clock";
    pub_config.queue_size = 10;
    publisher_clock =
        node_handle->communication_manager()
            .advertise<mmessage::mtime_mfrmsgs::MFRMessageClock>(pub_config);
    pub_config.topic_name = "/clock_expect";
    publisher_expect =
        node_handle->communication_manager()
            .advertise<mmessage::mtime_mfrmsgs::MFRMessageClock>(pub_config);
    pub_config.topic_name = "/clock_diff";
    publisher_diff =
        node_handle->communication_manager()
            .advertise<mmessage::mtime_mfrmsgs::MFRMessageClock>(pub_config);
  }
  puts("send node on_init()");

  return true;
}

void MFRSimulationNode::on_finish() {}
void MFRSimulationNode::on_running(const mfr::MFRNodeRunningInfo &info) {
  if (info.trigger == MFR_NODE_TRIGGER_TIME) {
    if (!publisher_clock->is_paused()) {
      static int value = 0;
      value++;
      mmessage::mtime_mfrmsgs::MFRMessageClock clock;
      clock.set_clock(value);
      publisher_clock->publish(clock);
      if (value % 10 == 0) {
        fprintf(stderr, "send expect with value %lu\n", clock.clock());
        publisher_expect->publish(clock);
      }
      if (value % 10 == 0) {
        fprintf(stderr, "send clock diff with value %lu\n", clock.clock());
        publisher_diff->publish(clock);
      }
    }
  }
}

static bool MFRSimulationNode_register_value __attribute__((unused)){
    (fprintf(stderr, "kkkkkkkkk\n"),
     mfr::MFRNodeFactory::instance().register_by_name(
         MFRSendNodeConstants::node_type,
         []() { return mmemory::MFMakeShared<MFRSimulationNode>(); }))};

} // namespace tmgr

using namespace tmgr;

int main() {

  char *distributed_mode = getenv("MFR_DISTRIBUTED_MODE");
  if (distributed_mode == nullptr) {
    (void)setenv("MFR_DISTRIBUTED_MODE", "1", 1);
  }

  mmemory::MFString machine_url;
  if (const char *env{std::getenv("MFR_MASTER_URI")}) {
    machine_url = env;
  } else {
    machine_url = MFRSendNodeConstants::default_master_uri;
  }
  std::string node_name{MFRSendNodeConstants::node_name};

  mfr::MFRNodeConfig node_config{};
  node_config.node_type = MFRSendNodeConstants::node_type;
  node_config.node_name = node_name.c_str();
  node_config.node_param_yaml = MFRSendNodeConstants::node_yaml;
  mfr::MFRNodeMachine::instance().register_node(node_config);

  std::string machine_name{MFRSendNodeConstants::machine_name};

  mfr::MFRMachineConfig machine_config{};
  machine_config.machine_url = machine_url;
  machine_config.machine_name = machine_name.c_str();
  machine_config.machine_param_yaml = MFRSendNodeConstants::machine_yaml;
  (void)mfr::MFRNodeMachine::instance().init(machine_config);
  mfr::MFRNodeMachine::instance().run();

  while (true) {
    sleep(10);
    puts("after sleep");
  }

  return 0;
}