#include <iostream>
#include <chrono>
#include <sys/mman.h>
#include <cstring>
#include <cstdlib>
#include <fcntl.h>     // posix_madvise, POSIX_MADV_DONTNEED
#include <unistd.h>

int main() {
    const size_t ALLOC_SIZE = 100 * 1024 * 1024; // 100MB
    void* ptr = mmap(NULL, ALLOC_SIZE, PROT_READ | PROT_WRITE, MAP_PRIVATE | MAP_ANON, -1, 0);
    if (ptr == MAP_FAILED) {
        std::cerr << "mmap failed to allocate memory\n";
        return 1;
    }

    // 初始化内存，确保物理页被实际分配
    std::memset(ptr, 0xFF, ALLOC_SIZE);

    std::cout << "PID: " << getpid() << "\n";
    std::cout << "Memory allocated and initialized.\n";
    std::cout << "Run 'pidin mem " << getpid() << "' before posix_madvise.\n";
    std::cout << "Press Enter to continue...\n";
    std::cin.get();

    auto start = std::chrono::high_resolution_clock::now();
    int ret = posix_madvise(reinterpret_cast<char*>(ptr), ALLOC_SIZE, POSIX_MADV_DISCARD_NP);
    auto end = std::chrono::high_resolution_clock::now();

    if (ret != 0) {
        std::cerr << "posix_madvise failed with error: " << ret << "\n";
    } else {
        std::cout << "posix_madvise(POSIX_MADV_DONTNEED) completed.\n";
    }

    auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    std::cout << "posix_madvise(POSIX_MADV_DONTNEED) time: " << duration << " microseconds\n";

    std::cout << "Now run: pidin mem " << getpid() << " again to see if there's a change.\n";
    std::cout << "Press Enter to free...\n";
    std::cin.get();

    free(ptr);

    std::cout << "Press Enter to exit...\n";
    std::cin.get();

    return 0;
}

