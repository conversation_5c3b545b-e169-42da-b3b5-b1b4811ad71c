find_program(CONAN conan)
if (NOT CONAN)
    message(FATAL_ERROR "No conan available, you may download the conan firstly")
endif ()

if (NOT EXISTS $ENV{MF_SYSTEM_ROOT_DIR}/package/utils.cmake)
    message(FATAL_ERROR "No conan setting available, you may download the conan setting firstly!")
endif()

set(CONAN_INSTALL_ARGS "--generator cmake")
include($ENV{MF_SYSTEM_ROOT_DIR}/package/utils.cmake)
conan_install()
include(${CONAN_BUILD_INFO_CMAKE})
conan_basic_setup()

set(CMAKE_EXPORT_COMPILE_COMMANDS ON)
