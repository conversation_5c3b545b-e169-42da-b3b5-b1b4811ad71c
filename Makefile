PROJECT_SOURCE_DIR ?= $(abspath ./)
BUILD_DIR ?= $(PROJECT_SOURCE_DIR)/build
INSTALL_DIR ?= $(BUILD_DIR)/install
CONAN_PKG_DIR ?= $(BUILD_DIR)/conan_package
DEPLOY_DIR ?= $(BUILD_DIR)/deploy
NUM_JOB ?= 5
CONAN_REMOTE := conan-pl
BUILD_TYPE ?= RelWithDebInfo

ifneq ($(MF_SYSTEM_ROOT_DIR), )
    MF_SYSTEM_ROOT := ${MF_SYSTEM_ROOT_DIR}
endif
MF_SYSTEM_ROOT ?= ./mf_system

include ${MF_SYSTEM_ROOT}/package/utils.mk
BUILD_ENVS_JSON := ${MF_SYSTEM_ROOT}/package/build_envs.json
RUN_IN_BUILD_ENV_PY := ${MF_SYSTEM_ROOT}/package/run_in_build_env.py

all:
	@echo nothing special
clean:
	rm -rf $(BUILD_DIR)

# always lint your code: https://confluence.momenta.works/pages/viewpage.action?pageId=138928951
# python3 -m pip install --extra-index-url https://artifactory.momenta.works/artifactory/api/pypi/pypi-momenta/simple mdk_tools -U
lint: lintcpp lintcmake
lintcpp:
	# python3 -m mdk_tools.cli.cmake_lint_install . # 安装格式配置文件
	python3 -m mdk_tools.cli.cpp_lint .
lintcmake:
	# python3 -m mdk_tools.cli.cmake_lint_install . # 安装格式配置文件
	python3 -m mdk_tools.cli.cmake_lint .

PACKAGE_NAME := obf_time_manager

# Add the package version suffix if you want to upload a tmp package for test or fix
# PACKAGE_VERSION_SUFFIX := test
PACKAGE_VERSION := $(shell cd $(PROJECT_SOURCE_DIR) && git describe --tag --abbrev=0)
ifneq ($(PACKAGE_VERSION_SUFFIX), )
	PACKAGE_VERSION := $(PACKAGE_VERSION)_$(PACKAGE_VERSION_SUFFIX)
else
	PACKAGE_VERSION := $(shell cd $(PROJECT_SOURCE_DIR) && git describe --tags)
endif

CMAKE_EXTRA_ARGS ?= # 留作接口，勿用

# NOTE: use ifneq to test
ifeq ($(BENV_ID), refcar_8620P_qnx_with_sdp_710)
  TIME_MANAGER_BUILD_ARGS := -DUSE_TIME_SYNC=ON
  TIME_MANAGER_BUILD_ARGS += -DUSE_SYSTEM_TIME=ON
	TIME_MANAGER_BUILD_ARGS += -DUSE_TIME_GAP=ON
  TIME_MANAGER_BUILD_ARGS += -DUSE_DM=ON
else ifeq ($(BENV_ID), refcar_8650P_qnx_with_sdp_710)
  TIME_MANAGER_BUILD_ARGS := -DUSE_TIME_SYNC=ON
  TIME_MANAGER_BUILD_ARGS += -DUSE_SYSTEM_TIME=ON
	TIME_MANAGER_BUILD_ARGS += -DUSE_TIME_GAP=ON
  TIME_MANAGER_BUILD_ARGS += -DUSE_DM=ON
else ifeq ($(BENV_ID), bgans_8650_qnx)
  TIME_MANAGER_BUILD_ARGS := -DUSE_TIME_SYNC=ON
  TIME_MANAGER_BUILD_ARGS += -DUSE_SYSTEM_TIME=ON
	TIME_MANAGER_BUILD_ARGS += -DUSE_TIME_GAP=ON
  TIME_MANAGER_BUILD_ARGS += -DUSE_DM=ON
else ifeq ($(BENV_ID), TMAS_f_QC8650P_QNX)
  TIME_MANAGER_BUILD_ARGS := -DUSE_TIME_SYNC=ON
  TIME_MANAGER_BUILD_ARGS += -DUSE_SYSTEM_TIME=ON
	TIME_MANAGER_BUILD_ARGS += -DUSE_TIME_GAP=ON
  TIME_MANAGER_BUILD_ARGS += -DUSE_DM=ON
else ifeq ($(BENV_ID), qwangs_8650P_qnx_with_sdp_710)
  TIME_MANAGER_BUILD_ARGS := -DUSE_TIME_SYNC=ON
  TIME_MANAGER_BUILD_ARGS += -DUSE_SYSTEM_TIME=ON
	TIME_MANAGER_BUILD_ARGS += -DUSE_TIME_GAP=OFF
  TIME_MANAGER_BUILD_ARGS += -DUSE_DM=OFF
else ifeq ($(BENV_ID), wys_qc8775_qnx)
  TIME_MANAGER_BUILD_ARGS := -DUSE_TIME_SYNC=ON
  TIME_MANAGER_BUILD_ARGS += -DUSE_SYSTEM_TIME=ON
	TIME_MANAGER_BUILD_ARGS += -DUSE_TIME_GAP=OFF
  TIME_MANAGER_BUILD_ARGS += -DUSE_DM=OFF
else
  TIME_MANAGER_BUILD_ARGS := -DUSE_TIME_SYNC=OFF
  TIME_MANAGER_BUILD_ARGS += -DUSE_SYSTEM_TIME=OFF
	TIME_MANAGER_BUILD_ARGS += -DUSE_TIME_GAP=OFF
  TIME_MANAGER_BUILD_ARGS += -DUSE_DM=OFF
endif

CMAKE_ARGS := \
	-DCMAKE_INSTALL_PREFIX=$(INSTALL_DIR) \
	-DBUILD_SHARED_LIBS=ON \
	-DCMAKE_VERBOSE_MAKEFILE=ON \
	-DENABLE_CONAN_BUILD=ON \
	-DCMAKE_BUILD_TYPE=$(BUILD_TYPE) \
	$(TIME_MANAGER_BUILD_ARGS) \
	$(CAMKE_EXTRA_ARGS)

safe_directory:
	git config --global --add safe.directory $(PROJECT_SOURCE_DIR)

prepare:
	make default_prepare
build: safe_directory
	make default_build
package: safe_directory
	make default_package
upload: package
	make default_upload
deploy: package
	make default_deploy
project: build 
	make dev_deploy

.PHONY: build package upload deploy project

RUN_IN_BUILD_ENV_BATCH_ARGS := --includes u16 u18 u20 mdc qnx devcar orin mdc610 mdc610_wulingshan --

DEPLOY_MODULE_DIR := $(DEPLOY_DIR)/modules/$(PACKAGE_NAME)
DEPLOY_COMMON_DIR := $(DEPLOY_DIR)/common
dev_deploy:
	make deploy
	@echo $(DEPLOY_MODULE_DIR)
	@echo $(DEPLOY_COMMON_DIR)
	@echo "move some library to target directory (common)"
	mkdir -p $(DEPLOY_COMMON_DIR)/include
	mkdir -p $(DEPLOY_COMMON_DIR)/lib

	rm -rf $(DEPLOY_MODULE_DIR)

	@echo "move sample to target directory (sample)"
	mkdir -p $(DEPLOY_MODULE_DIR)/sample

	@echo "move test to target directory (test)"
	mkdir -p $(DEPLOY_MODULE_DIR)/test
	cp -rf $(BUILD_DIR)/bin/*test $(DEPLOY_MODULE_DIR)/test

	@echo exported deploy dir to $(DEPLOY_DIR)
	rm $(DEPLOY_DIR).tar.gz
	tar cvzf $(DEPLOY_DIR).tar.gz \
		-C $(shell dirname $(DEPLOY_DIR)) \
		$(shell basename $(DEPLOY_DIR))
	@echo 'created "$(DEPLOY_DIR).tar.gz", ready to deploy!'

BUILD_ENV_ID ?= $(shell hostname)
URL_PREFIX ?= https://artifactory.momenta.works/artifactory/mdk-sample-data/ci-releases/$(PACKAGE_NAME)/$(shell cd $(PROJECT_SOURCE_DIR) && git log -1 --date=format:'%Y.%m.%d_%H.%M.%S' --format="%cd")_$(shell cd $(PROJECT_SOURCE_DIR) && git log -1 --format=%h --abbrev=8)/$(PACKAGE_VERSION)/$(BUILD_ENV_ID)
FILE_TO_UPLOAD ?=
upload_artifact:
	@test -f $(FILE_TO_UPLOAD) && \
	curl -T $(FILE_TO_UPLOAD) $(URL_PREFIX)/$(FILE_TO_UPLOAD) || \
	(echo '"$(FILE_TO_UPLOAD)" is not a file' && exit 1)

upload_conan_package_targz:
	mkdir -p $(CONAN_PKG_DIR) && cd $(CONAN_PKG_DIR) && \
	conan install $(PACKAGE_ID) --profile $(CONAN_PROFILE) -g=deploy && \
	tar cvzf package.tar.gz -C $(PACKAGE_NAME) `ls $(PACKAGE_NAME)` && \
	curl -T package.tar.gz $(URL_PREFIX)/package.tar.gz
upload_deploy_targz:
	curl -T $(BUILD_DIR)/deploy.tar.gz $(URL_PREFIX)/deploy.tar.gz

build_deploys:
	# python3 $(RUN_IN_BUILD_ENV_PY) --build-env $(BUILD_ENVS_JSON)@u16 make build deploy
	python3 $(RUN_IN_BUILD_ENV_PY) --build-env $(BUILD_ENVS_JSON)@orin make build deploy
	python3 $(RUN_IN_BUILD_ENV_PY) --build-env $(BUILD_ENVS_JSON)@devcar make build deploy
	# ls -alh build/repo/u16/deploy.tar.gz
	ls -alh build/repo/orin/deploy.tar.gz
	ls -alh build/repo/devcar/deploy.tar.gz
