#pragma once
#include "em/em_plugin.h"
#include "em/error_code.h"
#include "tmlog.h"
#include <cstring>
#include <dlfcn.h>
#include <fcntl.h>
#include <string>
#include <sys/stat.h>
#include <sys/types.h>

class EMWrapper {
public:
  static constexpr auto kObflaunchPluginEm = "OBFLAUNCH_PLUGIN_EM";
  static constexpr auto kRedirectConsoleLog = "OBFLAUNCH_REDIRECT_CONSOLE_LOG";

  EMWrapper() = default;
  ~EMWrapper() {
    if (nullptr == em_) {
      return;
    }
    obf_em_delete_sdk_(em_);
    em_ = nullptr;
  }

  bool try_init() {
    const char *library_path{std::getenv(kObflaunchPluginEm)};
    if (nullptr == library_path) {
      return false;
    }

    auto handle = dlopen(library_path, RTLD_NOW | RTLD_GLOBAL);
    if (nullptr == handle) {
      LOG_ERR("[ObflaunchPluginEm] Open library [%s] error: %s.", library_path,
              dlerror());
      return false;
    }

    obf::em::EMSdk *(*obf_em_create_sdk)();
    if (auto p = dlsym(handle, "obf_em_create_sdk")) {
      *(void **)(&obf_em_create_sdk) = p;
    } else {
      LOG_ERR("[ObflaunchPluginEm] library [%s] missing obf_em_create_sdk: %s.",
              library_path, dlerror());
      return false;
    }
    if (auto p = dlsym(handle, "obf_em_delete_sdk")) {
      *(void **)(&obf_em_delete_sdk_) = p;
    } else {
      LOG_ERR("[ObflaunchPluginEm] library [%s] missing obf_em_delete_sdk: %s.",
              library_path, dlerror());
      return false;
    }
    em_ = obf_em_create_sdk();

    is_init_ = (em_->init() == obf::em::ErrorCode::kSuccess);
    if (!is_init_) {
      LOG_ERR("obflaunch fail init EM. run in normal mode");
      return false;
    }
    LOG_INFO("obflaunch in EM mode");
    if (!handle_console_log()) {
      return false;
    }
    return true;
  }
  bool is_init() const { return is_init_; }

  bool handle_console_log() const {
    const char *console_log_path{std::getenv(kRedirectConsoleLog)};
    if (nullptr == console_log_path) {
      return true;
    }
    int fd = ::open(console_log_path, O_WRONLY | O_CREAT, 644);
    if (fd < 0) {
      LOG_ERR("dup file failed. open %s failed. errno:%d %s", console_log_path,
              errno, strerror(errno));
      return false;
    }
    (void)dup2(fd, STDOUT_FILENO);
    (void)dup2(fd, STDERR_FILENO);
    (void)close(fd);
    return true;
  }

  obf::em::EMSdk *get() { return em_; }

private:
  bool is_init_ = false;
  obf::em::EMSdk *em_ = nullptr;
  void (*obf_em_delete_sdk_)(const obf::em::EMSdk *);
};
