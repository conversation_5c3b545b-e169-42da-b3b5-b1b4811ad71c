#include "time_manager_config.h"

namespace tmgr {
const std::string TimeManagerConfig::SystemClockRecordFileName =
    "/opt/m/logs/time_manager_record.log"; // TODO: need fix this using config
const std::string TimeManagerConfig::OTServerSpecifier = "ts_ot";
const std::string TimeManagerConfig::WTSharedMemoryName = "/ts_tm";
const std::string TimeManagerConfig::WTHTTPServerURL = "www.baidu.com";

// MQTT配置参数实现
const std::string TimeManagerConfig::MQTTBrokerHost = "localhost";
const std::string TimeManagerConfig::MQTTTopic = "/time_manager/diff";
const std::string TimeManagerConfig::MQTTClientId = "time_manager_client";
} // namespace tmgr

std::string tmgr::DMErrorCodeToString(DMErrorCode error_code) {
  switch (error_code) {
  case DMErrorCode::DM_ERR_TM_UTC_INVALID:
    return "DM_ERR_TM_UTC_INVALID";
  case DMErrorCode::DM_ERR_TM_UTC_TIMEOUT:
    return "DM_ERR_TM_UTC_TIMEOUT";
  case DMErrorCode::DM_ERR_TM_GET_OT_ERROR:
    return "DM_ERR_TM_GET_OT_ERROR";
  case DMErrorCode::DM_ERR_TM_TIME_GAP_TIMEOUT:
    return "DM_ERR_TM_TIME_GAP_TIMEOUT";
  default:
    return "Unknown DMErrorCode";
  }
}