#include "data_types.h"
#include <string>

namespace tmgr {
struct Utility {
  static std::string WTSyncStatusToString(WTSyncStatus status) {
    switch (status) {
    case WT_STATE_INITIALIZED:
      return "WT_STATE_INITIALIZED";
    case WT_STATE_TIMEOUT:
      return "WT_STATE_TIMEOUT";
    case WT_STATE_JUMPED:
      return "WT_STATE_JUMPED";
    case WT_STATE_SYNCHRONIZED:
      return "WT_STATE_SYNCHRONIZED";
    case WT_STATE_COUNT:
      return "WT_STATE_COUNT";
    case WT_STATE_FROM_HTTP:
      return "WT_STATE_FROM_HTTP";
    default:
      return "Unknown WTSyncStatus";
    }
  }

  static std::string OTMasterSyncStatusToString(OTMasterSyncStatus status) {
    switch (status) {
    case OT_MASTER_NOT_SYNCHRONIZED:
      return "OT_MASTER_NOT_SYNCHRONIZED";
    case OT_MASTER_SYNC_UNLOCKED:
      return "OT_MASTER_SYNC_UNLOCKED";
    case OT_MASTER_SYNC_LOCKED:
      return "OT_MASTER_SYNC_LOCKED";
    default:
      return "Unknown OTMasterSyncStatus";
    }
  }

  static std::string RunningModeToString(RunningMode mode) {
    switch (mode) {
    case TM_NORMAL_MODE:
      return "Normal Mode";
    case TM_SIMULATION_MODE:
      return "Simulation Mode";
    default:
      return "Unknown RunningMode";
    }
  }
};
} // namespace tmgr