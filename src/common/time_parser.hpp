#pragma once

#include "tmlog.h"
#include <chrono>
#include <iomanip>
#include <sstream>

namespace tmgr {
struct TimeParser {
public:
  static std::chrono::system_clock::time_point
  ParseFromFormattedString(const std::string &timeStr,
                           const std::string &format) {
    std::tm tm = {};
    std::istringstream ss(timeStr);
    ss >> std::get_time(&tm, format.c_str());

    if (ss.fail()) {
      LOG_ERR("[TimeParser]: Failed to parse time: %s, return now()",
              timeStr.c_str());
      return std::chrono::system_clock::now();
    }

    auto timeT = std::mktime(&tm);
    if (timeT == -1) {
      LOG_ERR("[TimeParser]: mktime conversion failed, cannot set time, return "
              "now()");
      return std::chrono::system_clock::now();
    }

    return std::chrono::system_clock::from_time_t(timeT);
  }
};
} // namespace tmgr