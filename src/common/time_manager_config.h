#pragma once

#include <cstdint>
#include <string>
#include <sys/types.h>

namespace tmgr {
struct TimeManagerConfig {
  static const std::string SystemClockRecordFileName;
  static const int RecordDiskWriteIntervalMillSec = 20000;
  static const std::string OTServerSpecifier;
  static const std::string WTSharedMemoryName;
  static const std::string WTHTTPServerURL;
  static const int WTStatesCheckTimeoutMs = 15000;
  static const int HeartbeatMonitorIntervalMillSec = 100;
  static const int DidReporterIntervalMillSec = 500;
};

enum DMErrorCode : uint16_t {
  DM_ERR_TM_UTC_INVALID = 0X9602,
  DM_ERR_TM_UTC_TIMEOUT = 0X9603,
  DM_ERR_TM_GET_OT_ERROR = 0X9604,
  DM_ERR_TM_TIME_GAP_TIMEOUT = 0X9605
};

std::string DMErrorCodeToString(DMErrorCode error_code);

} // namespace tmgr