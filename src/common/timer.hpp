#pragma once

#include <atomic>
#include <condition_variable>
#include <functional>
#include <mutex>
#include <thread>
#include <unistd.h>

class Timer {
public:
  Timer() : is_running_(false) {}

  ~Timer() { stop(); }

  void start(int interval_ms, std::function<void()> callback) {
    if (is_running_) {
      stop();
    }
    is_running_.store(true);
    timer_thread_ = std::thread([this, interval_ms, callback]() {
      std::unique_lock<std::mutex> lock(mutex_);
      while (is_running_) {
        if (cv_.wait_for(lock, std::chrono::milliseconds(interval_ms),
                         [this] { return !is_running_; })) {
          break;
        }
        callback();
      }
    });
  }

  void stop() {
    is_running_.store(false);
    cv_.notify_all();
    if (timer_thread_.joinable()) {
      timer_thread_.join();
    }
  }

private:
  std::thread timer_thread_;
  std::atomic<bool> is_running_;
  std::mutex mutex_;
  std::condition_variable cv_;
};
