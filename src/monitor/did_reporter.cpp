#include "did_reporter.h"
#include "ara/dm/message_client.h"
#include "data_types.h"
#include "ot_consumer.h"
#include "tmlog.h"
#include "wt_calculator.h"
#include "wt_serializer.h"

namespace tmgr {

static void writeInt64BigEndian(std::vector<uint8_t> &buf, size_t offset,
                                int64_t value) {
  for (int i = 0; i < 8; ++i) {
    buf[offset + i] = static_cast<uint8_t>((value >> ((7 - i) * 8)) & 0xFF);
  }
}

static constexpr uint16_t kDidAFF1 = 0xAFF1;
static constexpr size_t kDidAFF1TotalLen = 25;

DidReporter::DidReporter() : is_running_(false) {}

DidReporter::~DidReporter() { stop(); }

void DidReporter::start(uint32_t interval_ms) {
  if (is_running_) {
    stop();
  }
  is_running_ = true;
  reporter_timer_.start(interval_ms, [this]() { reportDidInfo(); });
}

void DidReporter::stop() {
  is_running_ = false;
  reporter_timer_.stop();
}

void DidReporter::reportDidInfo() {
  std::vector<uint8_t> did_data(kDidAFF1TotalLen, 0);

  auto ot_timestamp_ns = tmgr::OTConsumer::getInstance()
                             .GetCurrentTime()
                             .time_since_epoch()
                             .count(); // int64_t

  auto wt_time_point = tmgr::WTCalculator::calculateWTTime(false);
  auto wt_timestamp_ns = wt_time_point.time_since_epoch().count(); // int64_t

  auto otStatus = tmgr::OTConsumer::getInstance().GetTimeWithStatus();
  bool otSynced = otStatus.GetSynchronizationStatus() ==
                  obf::ts::SynchronizationStatus::kSynchronized;

  auto wt_info = tmgr::WTSerializer::getInstance().readWTInfo();
  bool wtSynced = true;
  if (wt_info.wt_sync_status == tmgr::WT_STATE_TIMEOUT ||
      wt_info.wt_sync_status == tmgr::WT_STATE_INITIALIZED ||
      wt_info.wt_sync_status == tmgr::WT_STATE_FROM_HTTP) {
    wtSynced = false;
  }

  // write wt info into did_data[8...15] (9-16 bytes)
  writeInt64BigEndian(did_data, 8, ot_timestamp_ns);

  // write wt info into did_data[16...23] (17-24 bytes)
  writeInt64BigEndian(did_data, 16, wt_timestamp_ns);

  // did_data[24] bit6 means EGT time status - ot status
  // did_data[24] bit5 means UTC time status - wt status
  // 1 means synced / 0 means timeout or lost
  uint8_t lastByte = 0;
  if (otSynced) {
    lastByte |= (1 << 6);
  }
  if (wtSynced) {
    lastByte |= (1 << 5);
  }
  did_data[24] = lastByte;

  ara::dm::SyncDidInfo did_info;
  did_info.did = kDidAFF1;
  did_info.did_len = static_cast<uint16_t>(did_data.size());
  did_info.did_data = did_data;

  std::vector<ara::dm::SyncDidInfo> didInfos;
  didInfos.push_back(did_info);

  ara::dm::MessageClient::GetInst().SetDidInfo(didInfos, false);

  // 500ms once, so 20 times means 10s output once
  static int printCount = 0;
  if (++printCount % 20 == 0) {
    printCount = 0;

    char output_buffer[2048];
    memset(output_buffer, 0, sizeof(output_buffer));
    char *ptr = output_buffer;

    ptr +=
        snprintf(ptr, sizeof(output_buffer) - (ptr - output_buffer),
                 "[DidReporter] Write AFF1 DID Info, Buffer Content (Hex):\n");
    for (size_t i = 0; i < did_data.size(); ++i) {
      ptr += snprintf(ptr, sizeof(output_buffer) - (ptr - output_buffer),
                      "%02X ", did_data[i]);
    }

    ptr += snprintf(ptr, sizeof(output_buffer) - (ptr - output_buffer), "\n");

    int64_t ot_ts_deserialized =
        ((int64_t)did_data[8] << 56) | ((int64_t)did_data[9] << 48) |
        ((int64_t)did_data[10] << 40) | ((int64_t)did_data[11] << 32) |
        ((int64_t)did_data[12] << 24) | ((int64_t)did_data[13] << 16) |
        ((int64_t)did_data[14] << 8) | ((int64_t)did_data[15]);

    int64_t wt_ts_deserialized =
        ((int64_t)did_data[16] << 56) | ((int64_t)did_data[17] << 48) |
        ((int64_t)did_data[18] << 40) | ((int64_t)did_data[19] << 32) |
        ((int64_t)did_data[20] << 24) | ((int64_t)did_data[21] << 16) |
        ((int64_t)did_data[22] << 8) | ((int64_t)did_data[23]);

    ptr +=
        snprintf(ptr, sizeof(output_buffer) - (ptr - output_buffer),
                 "OT Timestamp (Deserialized): %lld ns\n"
                 "WT Timestamp (Deserialized): %lld ns\n",
                 (long long)ot_ts_deserialized, (long long)wt_ts_deserialized);

    ptr += snprintf(ptr, sizeof(output_buffer) - (ptr - output_buffer),
                    "Last Byte (Index 24) Status Bits (Binary):\n");
    for (int bit = 7; bit >= 0; --bit) {
      ptr += snprintf(ptr, sizeof(output_buffer) - (ptr - output_buffer),
                      "Bit[%d]: %d\n", bit, (did_data[24] >> bit) & 1);
    }

    LOG_INFO("%s", output_buffer);
  }
}

} // namespace tmgr