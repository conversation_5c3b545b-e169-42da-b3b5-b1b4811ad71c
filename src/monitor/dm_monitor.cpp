#include "dm_monitor.h"
#include "ara/dm/message_client.h"
#include "tmlog.h"

using namespace tmgr;

DMMonitor &DMMonitor::getInstance() {
  static DMMonitor instance;
  return instance;
}

std::atomic<bool> &DMMonitor::getAtomicFlag(DMErrorCode error_code) {
  switch (error_code) {
  case DMErrorCode::DM_ERR_TM_UTC_INVALID:
    return last_report_tm_utc_invalid_;
  case DMErrorCode::DM_ERR_TM_UTC_TIMEOUT:
    return last_report_tm_utc_timeout_;
  case DMErrorCode::DM_ERR_TM_GET_OT_ERROR:
    return last_report_tm_get_ot_error_;
  case DMErrorCode::DM_ERR_TM_TIME_GAP_TIMEOUT:
    return last_report_tm_time_gap_timeout_;
  default:
    LOG_ERR("[DMMonitor]: Unknown DMErrorCode from atomic flag: %d",
            static_cast<int>(error_code));
    // use dummy one to prevent UB
    static std::atomic<bool> invalid_flag{false};
    return invalid_flag;
  }
}

void DMMonitor::reportDMErrorCode(DMErrorCode error_code, bool enable) {
  if (!dm_enabled_) {
    return;
  }

  std::atomic<bool> &flag = getAtomicFlag(error_code);

  bool expected = flag.load();

  if (enable) {
    // expected == false means not report before, can report now
    if (!expected) {
      // try to cas expected from false to true
      if (flag.compare_exchange_strong(expected, true)) {
        LOG_INFO("[DMMonitor]: Report DMErrorCode: 0x%04X[%s], enable: %s",
                 error_code, DMErrorCodeToString(error_code).c_str(),
                 enable ? "true" : "false");
        ara::dm::MessageClient::GetInst().ReportMonitorAction(error_code,
                                                              enable);
      }
      // if cas fails, means other thread just report it now, so we should not
      // report again
    }
    // if expected == true, means already report before, no need to report again
  } else {
    // only when report true beforen, then we will report it as false
    if (expected) {
      // try to cas from true to false
      if (flag.compare_exchange_strong(expected, false)) {
        LOG_INFO("[DMMonitor]: Report DMErrorCode: 0x%04X[%s], enable: %s",
                 error_code, DMErrorCodeToString(error_code).c_str(),
                 enable ? "true" : "false");
        ara::dm::MessageClient::GetInst().ReportMonitorAction(error_code,
                                                              enable);
      }
      // if cas fails, means any other thread just report it now, so we should
      // not report again
    }
    // means no true before, so no need to report false
  }
}

bool DMMonitor::isDMEnabled() { return dm_enabled_; }

void DMMonitor::setDMEnabled(bool dm_enabled) { dm_enabled_ = dm_enabled; }