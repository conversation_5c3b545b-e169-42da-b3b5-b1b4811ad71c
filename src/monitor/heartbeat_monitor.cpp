#include "heartbeat_monitor.h"

#include "timer.hpp"
#include "tmlog.h"
#include "wt_serializer.h"

namespace tmgr {
HeartbeatMonitor::HeartbeatMonitor(int updateTimestampMillsec) {
  LOG_INFO("[HeartbeatMonitor]: Heartbeat monitor started with interval: %dms",
           updateTimestampMillsec);
  timer_.start(updateTimestampMillsec, []() {
    WTSerializer::getInstance().updateHeartbeatTimestamp();
  });
}

HeartbeatMonitor::~HeartbeatMonitor() { timer_.stop(); }

} // namespace tmgr