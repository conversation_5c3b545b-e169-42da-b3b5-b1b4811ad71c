#pragma once

#include "time_manager_config.h"
#include <atomic>

namespace tmgr {
class DMMonitor {
public:
  static DMMonitor &getInstance();
  void reportDMErrorCode(
      DMErrorCode error_code,
      bool enable); // true means error happens, false means error resolved
  bool isDMEnabled();
  void setDMEnabled(bool dm_enabled);

private:
  DMMonitor() = default;
  std::atomic<bool> &getAtomicFlag(DMErrorCode error_code);

private:
  std::atomic<bool> dm_enabled_{false};
  std::atomic<bool> last_report_tm_utc_invalid_{false};
  std::atomic<bool> last_report_tm_utc_timeout_{false};
  std::atomic<bool> last_report_tm_get_ot_error_{false};
  std::atomic<bool> last_report_tm_time_gap_timeout_{false};
};

} // namespace tmgr