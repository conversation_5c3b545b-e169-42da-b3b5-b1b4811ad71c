#include "wt_statuschecker.h"
#include "data_types.h"
#include "ot_consumer.h"
#include "tmlog.h"
#include "utility.hpp"
#include "wt_serializer.h"
#include <atomic>
#ifdef TM_USE_DM
#include "monitor/dm_monitor.h"
#endif

namespace tmgr {

WTStatusChecker::WTStatusChecker(int check_interval_ms, WTSyncMode timeMode)
    : check_interval_ms_(check_interval_ms), timeMode_(timeMode),
      wt_info_feeded_(false), time_gap_feeded_(false) {
  // reset part for wt info and time gap
  wt_info_.diff_seconds = 0;
  wt_info_.diff_nanoseconds = 0;
  wt_info_.wt_sync_status = WT_STATE_INITIALIZED;
  wt_info_.ot_master_sync_status = OT_MASTER_NOT_SYNCHRONIZED;
  previous_wt_info_ = wt_info_;
  WTSerializer::getInstance().writeWTInfo(
      wt_info_); // make sure shm's diff materials not be polluted with last
                 // time created by tm daemon, make it clean again
  WTSerializer::getInstance().writeTimeGap(0);

  if (timeMode_ == WTSyncMode::WT_SYNC_MODE_GAP) {
    LOG_INFO("[WTStatusChecker]: time gap mode, WTInfo init seralized to SHM "
             "successfully with "
             "diff_seconds: 0, diff_nanoseconds: 0, wt sync status: "
             "WT_STATE_INITIALIZED, time_gap: 0, ot_master_sync_status: "
             "OT_MASTER_NOT_SYNCHRONIZED");
  } else {
    LOG_INFO("[WTStatusChecker]: time normal mode, WTInfo init seralized to "
             "SHM successfully with "
             "diff_seconds: 0, diff_nanoseconds: 0, wt sync status: "
             "WT_STATE_INITIALIZED, ot_master_sync_status: "
             "OT_MASTER_NOT_SYNCHRONIZED");
  }

  timer_.start(check_interval_ms_, [this]() { checkAndUpdateStatus(false); });
}

WTStatusChecker::~WTStatusChecker() { timer_.stop(); }

// NOTE: this is triggerd by SOME/IP callback or HTTP client thread
void WTStatusChecker::feedWTInfo(const WTInfo &sample) {
  wt_info_feeded_ = true;

  // TODO: write diff / systemclock into filesystem
  wt_info_ = sample;

  // when receive new WTInfo, update the status immediately
  checkAndUpdateStatus(true);
}

void WTStatusChecker::feedWTInfoDog() { wt_info_feeded_ = true; }

void WTStatusChecker::feedTimeGap(int64_t time_gap) {
  if (timeMode_ == WTSyncMode::WT_SYNC_MODE_GAP) {
    {
      time_gap_feeded_ = true;
      time_gap_ = time_gap;
    }

    // when receive new time gap, update the status immediately
    checkAndUpdateStatus(true);
  }
}

void WTStatusChecker::feedTimeGapDog() {
  if (timeMode_ == WTSyncMode::WT_SYNC_MODE_GAP) {
    { time_gap_feeded_ = true; }
  }
}

void WTStatusChecker::checkAndUpdateStatus(bool force_update) {
  if (!force_update) // trigger by timer to check timeout, force_update should
                     // not check timeout
  {
    checkTimeout();
  }

  syncWTInfoToSHM();
}

void WTStatusChecker::checkTimeout() {
  // TODO: timeout logic with HTTP need discuss, currently just treat HTTP is
  // not considered into timeout scope
  if (wt_info_.wt_sync_status !=
      WT_STATE_FROM_HTTP) // HTTP not considered will be timeout
  {
    if (!wt_info_feeded_) // after timeout, if this flag not feed by time
                          // source, consider it is timeout
    {
      wt_info_.wt_sync_status = WT_STATE_TIMEOUT;
      LOG_WARN("[WTStatusChecker]: WT Sync Status is timeout, since no diff "
               "packet sent from MCU");
#ifdef TM_USE_DM
      if (tmgr::DMMonitor::getInstance().isDMEnabled()) {
        tmgr::DMMonitor::getInstance().reportDMErrorCode(
            tmgr::DMErrorCode::DM_ERR_TM_UTC_TIMEOUT, true);
      }
#endif
    } else {
      wt_info_feeded_ =
          false; // reset this flag to false, check for next timeout arrivel
#ifdef TM_USE_DM
      if (tmgr::DMMonitor::getInstance().isDMEnabled()) {
        tmgr::DMMonitor::getInstance().reportDMErrorCode(
            tmgr::DMErrorCode::DM_ERR_TM_UTC_TIMEOUT, false);
      }
#endif
    }

    if (timeMode_ == WTSyncMode::WT_SYNC_MODE_GAP) {
      if (!time_gap_feeded_) {
        wt_info_.wt_sync_status =
            WT_STATE_TIMEOUT; // TODO: maybe seperate with WT_STATE_TIMEOUT
        LOG_WARN("[WTStatusChecker]: WT Sync Status is timeout, since no time "
                 "gap packet sent from ICB");
#ifdef TM_USE_DM
        if (tmgr::DMMonitor::getInstance().isDMEnabled()) {
          tmgr::DMMonitor::getInstance().reportDMErrorCode(
              tmgr::DMErrorCode::DM_ERR_TM_TIME_GAP_TIMEOUT, true);
        }
#endif
      } else {
        time_gap_feeded_ = false;
#ifdef TM_USE_DM
        if (tmgr::DMMonitor::getInstance().isDMEnabled()) {
          tmgr::DMMonitor::getInstance().reportDMErrorCode(
              tmgr::DMErrorCode::DM_ERR_TM_TIME_GAP_TIMEOUT, false);
        }
#endif
      }
    }
  }
}

void WTStatusChecker::syncWTInfoToSHM() {
  // reduce sync SHM times, we add previous_wt_info_ to avoid this
  if (previous_wt_info_ != wt_info_) {
    WTSerializer::getInstance().writeWTInfo(wt_info_);
    LOG_INFO(
        "[WTStatusChecker]: WTInfo seralized to SHM successfully with "
        "diff_seconds: %d, "
        "diff_nanoseconds: %d, wt sync status: %s, ot_master_sync_status: %s "
        "(current ot is %lld)",
        wt_info_.diff_seconds.load(), wt_info_.diff_nanoseconds.load(),
        Utility::WTSyncStatusToString(wt_info_.wt_sync_status).c_str(),
        Utility::OTMasterSyncStatusToString(wt_info_.ot_master_sync_status)
            .c_str(),
        OTConsumer::getInstance().GetCurrentTime().time_since_epoch().count());
    previous_wt_info_ = wt_info_;
  }

  if (timeMode_ == WTSyncMode::WT_SYNC_MODE_GAP) {
    // same as previous_wt_info_, reduce sync times
    if (previous_time_gap_ != time_gap_) {
      WTSerializer::getInstance().writeTimeGap(time_gap_);
      LOG_DEBUG("[WTStatusChecker]: TimeGap seralized to SHM successfully with "
                "time_gap: %lld(ns)",
                time_gap_);
      previous_time_gap_ = time_gap_;
    }
  }
}

}; // namespace tmgr