#pragma once

#include "common/timer.hpp"
#include "data_types.h"
#include "iwt_statuscheck.h"

namespace tmgr {

class WTStatusChecker : public IWTStatusChecker {
public:
  WTStat<PERSON><PERSON><PERSON><PERSON>(int check_interval_ms, WTSyncMode timeMode);
  ~WTStatusChecker();
  void feedWTInfo(const WTInfo &sample) override;
  void feedWTInfoDog();
  void feedTimeGap(int64_t time_gap) override;
  void feedTimeGapDog();

private:
  void checkAndUpdateStatus(bool force_update);
  void syncWTInfoToSHM();
  void checkTimeout();

private:
  Timer timer_;
  int check_interval_ms_;
  WTSyncMode timeMode_;
  WTInfo wt_info_;
  int64_t time_gap_{0};
  int64_t previous_time_gap_{0};
  WTInfo previous_wt_info_;
  std::atomic<bool> wt_info_feeded_;
  std::atomic<bool> time_gap_feeded_;
};

} // namespace tmgr