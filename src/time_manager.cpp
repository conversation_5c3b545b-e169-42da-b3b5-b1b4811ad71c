#include "data_types.h"
#include "mfr_simulation_time_channel.h"
#include "monitor/heartbeat_monitor.h"
#include "monitor/running_mode_manager.h"
#include "tmlog.h"
#ifdef TM_USE_TIME_SYNC
#include "communication/http_time_channel.h"
#include "communication/someip_time_channel.h"
#ifdef TM_USE_TIME_GAP
#include "communication/someip_time_gap_channel.h"
#endif
#include "ot_consumer.h"
#endif
#ifdef TM_USE_SYSTEM_TIME
#include "st/system_clock_manager.h"
#endif
#include "em_wrapper.h"
#include "time_file_handler.h"
#include "time_manager_config.h"
#include "time_manager_sdk.h"
#include "wt_serializer.h"
#include "wt_shared_memory_handler.h"
#include <memory>
#include <signal.h>
#include <string.h>
#ifdef TM_USE_DM
#include "monitor/did_reporter.h"
#include "monitor/dm_monitor.h"
#endif

void printUsage() {
  tmgr::tm_print(
      stderr,
      "Usage: [options]\n"
      "Options:\n"
      "  -l log_file_name     Set the log file name.\n"
#ifdef TM_USE_SYSTEM_TIME
      "  -r record_file_name  Set the system timestamp record file name.\n"
#endif
      "  -v                   Set log level to DEBUG.\n"
      "  -s                   Enable simulation mode.\n"
      "  -g                   Set time mode to Gap Mode (using gap value "
      "from ICB through SOME/IP, WT Time = OT Time + diff (MCU) + Gap (ICB)).\n"
      "All options are optional.\n"
#ifdef TM_USE_DM
      "  -e                   Enable DM monitor to report its error code.\n"
#endif
  );
}

std::atomic<bool> running(true);
std::condition_variable cv;
std::mutex cv_m;

void stop_machine(int signum) {
  if (signum == SIGINT || signum == SIGTERM) {
    LOG_INFO("================================= Exiting TimeManager... "
             "==============================");
    running.store(false);
    cv.notify_all();
  }
}

int main(int argc, char *argv[]) {
  std::string logFileName;
  std::string recordFileName =
      tmgr::TimeManagerConfig::SystemClockRecordFileName;
  tmgr::LogLevel logLevel = tmgr::LOG_LEVEL_INFO;
  uint64_t heartbeatInterval =
      tmgr::TimeManagerConfig::HeartbeatMonitorIntervalMillSec;

  tmgr::RunningMode running_mode = tmgr::RunningMode::TM_NORMAL_MODE;
  tmgr::WTSyncMode timeMode = tmgr::WTSyncMode::WT_SYNC_MODE_NORMAL;

#ifdef TM_USE_DM
  bool dm_mode = false;
#endif

  for (int i = 1; i < argc; ++i) {
    if (strcmp(argv[i], "-l") == 0) {
      if (i + 1 < argc) {
        logFileName = argv[++i];
      } else {
        tmgr::tm_print(stderr,
                       "Option '-l' requires a file name, specify it, and "
                       "start TimeManager again.\n");
        return 1;
      }
    } else if (strcmp(argv[i], "-r") == 0) {
      if (i + 1 < argc) {
        recordFileName = argv[++i];
      } else {
        tmgr::tm_print(stderr,
                       "Option '-r' requires a file name, specify it, and "
                       "start TimeManager again.\n");
        return 1;
      }
    } else if (strcmp(argv[i], "-v") == 0) {
      logLevel = tmgr::LOG_LEVEL_DEBUG;
    } else if (strcmp(argv[i], "-t") == 0) {
      if (i + 1 < argc) {
        heartbeatInterval = std::stoull(argv[++i]);
      } else {
        tmgr::tm_print(stderr,
                       "Option '-t' requires a value, specify it, and start "
                       "TimeManager again.\n");
        return 1;
      }
    } else if (strcmp(argv[i], "-s") == 0) {
      running_mode = tmgr::RunningMode::TM_SIMULATION_MODE;
    } else if (strcmp(argv[i], "-g") == 0) {
      timeMode = tmgr::WTSyncMode::WT_SYNC_MODE_GAP;
      tmgr::tm_print(
          stderr,
          "[TimeManager]: Running in ===== Gap Mode =====, WT Time = OT Time + "
          "diff (MCU) + Gap (ICB)\n");
    }
#ifdef TM_USE_DM
    else if (strcmp(argv[i], "-e") == 0) {
      dm_mode = true;
      tmgr::tm_print(
          stderr, "[TimeManager]: Using DM Monitor to report its error code\n");
    }
#endif
    else {
      tmgr::tm_print(stderr, "Unrecognized option: %s\n", argv[i]);
      printUsage();
      return 1;
    }
  }

  if (timeMode != tmgr::WTSyncMode::WT_SYNC_MODE_GAP) {
    tmgr::tm_print(stderr,
                   "[TimeManager]: Running in ===== Normal Mode =====, WT "
                   "Time = OT Time + "
                   "diff (MCU)\n");
  }

  if (!logFileName.empty()) {
    tmgr::tm_print(
        stderr,
        "==================== TimeManager Daemon mode, log level is %d, "
        "all logs will transfer to file: %s ========================\n",
        logLevel, logFileName.c_str());
    tmgr::Logger::getInstance().init(logLevel, true, logFileName);
  } else {
    tmgr::tm_print(
        stderr,
        "==================== TimeManager Daemon mode, log level is %d "
        "========================\n",
        logLevel);
    tmgr::Logger::getInstance().init(logLevel, false);
  }

  EMWrapper em;
  (void)em.try_init();

  LOG_INFO("==================================== TimeManager Start with "
           "git revision %s, TimeManagerSDK git revision %s "
           "=========================================",
           TM_GIT_VERSION, tmgr::TimeManagerSDK::version().c_str());

  if (running_mode == tmgr::RunningMode::TM_SIMULATION_MODE) {
    LOG_INFO("================================ [TimeManager]: Running in "
             "simulation mode. ======================");
  } else {
    LOG_INFO("================================ [TimeManager]: Running in "
             "machine mode. ======================");
  }

#ifdef TM_USE_SYSTEM_TIME
  // ST Setup
  auto system_clock_manager = std::make_shared<tmgr::SystemClockManager>(
      recordFileName, tmgr::TimeManagerConfig::RecordDiskWriteIntervalMillSec);
#endif

#ifdef TM_USE_TIME_SYNC
  // TODO: maybe should move to first
  // OT Setup
  tmgr::OTConsumer::getInstance().init(
      tmgr::TimeManagerConfig::OTServerSpecifier);
#endif

  // ===============================================================================================================
  // ================================ WT Setup
  // =====================================================================
  tmgr::WTSerializer::getInstance().init(
      std::make_unique<tmgr::WTSharedMemoryHandler>(
          tmgr::TimeManagerConfig::WTSharedMemoryName));
  // ================================ all ***shared memory*** stuff should be
  // below
  // =======================================
  // ================================================================================================================

  // running isolation for monitoring
  tmgr::RunningModeManager running_mode_manager;
  running_mode_manager.setRunningMode(running_mode);
  running_mode_manager.setWTSyncMode(
      timeMode); // init shared memory's WTSyncMode, will only in this place

  std::unique_ptr<tmgr::MFRSimulationTimeChannel> mfr_simulation_channel;
  if (running_mode == tmgr::RunningMode::TM_SIMULATION_MODE) {
    mfr_simulation_channel =
        std::make_unique<tmgr::MFRSimulationTimeChannel>(running_mode);
  } else {
#ifdef TM_USE_TIME_SYNC
    mfr_simulation_channel =
        std::make_unique<tmgr::MFRSimulationTimeChannel>(running_mode);
#endif
  }

#ifdef TM_USE_DM
  if (dm_mode) {
    tmgr::DMMonitor::getInstance().setDMEnabled(true);
  } else {
    tmgr::DMMonitor::getInstance().setDMEnabled(false);
  }
#endif

#ifdef TM_USE_TIME_SYNC
  auto statusChecker = std::make_shared<tmgr::WTStatusChecker>(
      tmgr::TimeManagerConfig::WTStatesCheckTimeoutMs, timeMode);

  // HTTP Setup (get init timestamp from Server before someip)
  tmgr::HTTPTimeChannel http_channel(tmgr::TimeManagerConfig::WTHTTPServerURL,
                                     statusChecker);

  LOG_INFO("[TimeManager]: Creating SOME/IP Time Channel...");
  // SOME/IP Setup
  tmgr::SomeIPTimeChannel channel(statusChecker, system_clock_manager);

  if (!channel.init()) {
    LOG_ERR("[TimeManager]: Failed to initialize SomeIPTimeChannel, please "
            "check if SOC / MCU SOME/IP is working, exit(-1)");
    // for testing purpose, not return
    // return -1;
  } else {
    LOG_INFO("[TimeManager]: Successfully initialize SomeIPTimeChannel, begin "
             "to recevie time sync event from MCU");
  }

  LOG_INFO("[TimeManager]: Finish create SOME/IP Time Channel...");

#ifdef TM_USE_TIME_GAP
  std::unique_ptr<tmgr::SomeIPTimeGapChannel> gap_channel;
  if (timeMode == tmgr::WTSyncMode::WT_SYNC_MODE_GAP) {
    // if trigger gap mode, we should recive two someip packet
    // 1. from MCU within diff sec
    // 2. from ICB through SOME/IP with gap value
    gap_channel = std::make_unique<tmgr::SomeIPTimeGapChannel>(statusChecker);

    if (!gap_channel->init()) {
      LOG_ERR("[TimeManager]: **Failed** to initialize SomeIPTimeGapChannel, "
              "please check if ICB SOME/IP is working, exit(-1)");
      // for testing purpose, not return
      // return -1;
    } else {
      LOG_INFO("[TimeManager]: Successfully initialize SomeIPTimeGapChannel, "
               "begin to recevie gap sync event from ICB");
    }
  }
#endif
#endif

  std::unique_ptr<tmgr::HeartbeatMonitor> heartbeat_monitor;

  if (running_mode == tmgr::RunningMode::TM_SIMULATION_MODE) {
    heartbeat_monitor =
        std::make_unique<tmgr::HeartbeatMonitor>(heartbeatInterval);
  }

#ifdef TM_USE_DM
  std::unique_ptr<tmgr::DidReporter> did_reporter;
  if (dm_mode) {
    did_reporter = std::make_unique<tmgr::DidReporter>();
    did_reporter->start(tmgr::TimeManagerConfig::DidReporterIntervalMillSec);
  }
#endif

  if (em.is_init()) {
    (void)em.get()->register_exit_function([](int sig) {
      stop_machine(sig);
      return true;
    });
  } else {
    // signal setup
    struct sigaction action;
    memset(&action, 0, sizeof(action));
    action.sa_handler = stop_machine;

    if (sigaction(SIGINT, &action, nullptr) < 0) {
      LOG_ERR("Cannot set sigaction for SIGINT, exit(1)\n");
      return 1;
    }

    if (sigaction(SIGTERM, &action, nullptr) < 0) {
      LOG_ERR("Cannot set sigaction for SIGTERM, exit(1)\n");
      return 1;
    }
  }

  if (em.is_init()) {
    auto ret =
        em.get()->report_execution_state(obf::em::ExecutionState::kRunning);
    if (obf::em::ErrorCode::kSuccess != ret) {
      LOG_ERR("[TimeManager::EM]: fail report execution_state ret=%u", ret);
    }
  }

  LOG_INFO("[TimeManager]: Init successfully, enter daemon mode");

  std::unique_lock<std::mutex> lk(cv_m);
  cv.wait(lk, [] { return !running.load(); });

  tmgr::tm_print(
      stderr,
      "================================= TimeMananger exit main function, "
      "cleanup resources.... =================================\n");

  return 0;
}
