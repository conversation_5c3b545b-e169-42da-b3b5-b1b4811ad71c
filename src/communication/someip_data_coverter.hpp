#pragma once

#include "data_types.h"
#include "obf_cm_someip.h"

namespace tmgr {
struct SomeIPDataConverter {
public:
  static WTInfo convertToWTInfo(obf::cm::TimeSyncStruct sample,
                                bool use_onboard_diff) {
    WTInfo info{};

    if (use_onboard_diff) {
      info.diff_nanoseconds = sample.time_diff_onbard.diff_nanoseconds;
      info.diff_seconds = sample.time_diff_onbard.diff_seconds;
      info.ot_master_sync_status =
          (OTMasterSyncStatus)sample.time_diff_onbard.ot_sync_status;
    } else {
      info.diff_nanoseconds = sample.time_diff_vehicle.diff_nanoseconds;
      info.diff_seconds = sample.time_diff_vehicle.diff_seconds;
      info.ot_master_sync_status =
          (OTMasterSyncStatus)sample.time_diff_vehicle.ot_sync_status;
    }
    info.wt_sync_status =
        WT_STATE_SYNCHRONIZED; // feed by time source, so it is synchronized

    return info;
  }
};
} // namespace tmgr
