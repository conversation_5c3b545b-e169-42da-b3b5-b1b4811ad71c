#pragma once

#include "mfr/mfr.h"
#include "mfr_node_contants.h"

namespace tmgr {

class MFRSimulationNode : public mfr::MFRNode {
public:
  MFRSimulationNode();
  bool on_init(mfr::MFRNodeHandle *node_handle) override;
  void on_finish() override;
  void on_running(const mfr::MFRNodeRunningInfo &info) override;

private:
  mfr::MFRSubscriber *subscriber_{nullptr};
  bool use_enable_relative_time_{false};
  bool use_time_diff{false};
};

} // namespace tmgr