#pragma once

#include "service/gPTPTimeGap_SC/gPTPTimeGap_proxy.h"
#include "wt_statuschecker.h"

namespace tmgr {

class SomeIPTimeGapChannel {
public:
  SomeIPTimeGapChannel(std::shared_ptr<WTStatusChecker> wt_status_checker);
  ~SomeIPTimeGapChannel();
  bool init();

private:
  obf::cm::proxy::gPTPTimeGap_SC_gPTPTimeGap::gPTPTimeGap_Proxy proxy_{};
  obf::cm::TimeGapData time_gap_data{};
  std::shared_ptr<WTStatus<PERSON>hecker> wt_status_checker_;
};
} // namespace tmgr