#pragma once

#include "data_types.h"
#include "ot_consumer.h"
#include "tmlog.h"
#include <cstdint>

namespace tmgr {

/**
 * UDP报文格式定义
 * 假设UDP报文包含以下字段：
 * - diff_seconds (4字节)
 * - diff_nanoseconds (4字节)  
 * - ot_sync_status (1字节)
 * - timestamp (8字节) - 可选的时间戳字段
 */
struct UDPDiffPacket {
    int32_t diff_seconds;
    int32_t diff_nanoseconds;
    uint8_t ot_sync_status;
    uint8_t reserved[3];  // 对齐填充
    int64_t timestamp;    // 可选时间戳
} __attribute__((packed));

struct UDPDataConverter {
public:
    static WTInfo convertToWTInfo(const UDPDiffPacket& packet) {
        WTInfo info{};
        
        // 直接使用UDP报文中的diff数据
        info.diff_seconds = packet.diff_seconds;
        info.diff_nanoseconds = packet.diff_nanoseconds;
        info.ot_master_sync_status = static_cast<OTMasterSyncStatus>(packet.ot_sync_status);
        info.wt_sync_status = WT_STATE_SYNCHRONIZED; // UDP数据源认为是同步的
        
        LOG_INFO("[UDPDataConverter]: converted UDP packet to WTInfo: "
                 "diff_seconds=%d, diff_nanoseconds=%d, ot_sync_status=%d",
                 info.diff_seconds.load(), info.diff_nanoseconds.load(),
                 static_cast<int>(info.ot_master_sync_status.load()));
        
        return info;
    }
    
    static bool validatePacket(const void* data, size_t length) {
        if (length < sizeof(UDPDiffPacket)) {
            LOG_WARN("[UDPDataConverter]: packet too small, expected %zu bytes, got %zu",
                     sizeof(UDPDiffPacket), length);
            return false;
        }
        
        const UDPDiffPacket* packet = static_cast<const UDPDiffPacket*>(data);
        
        // 基本的数据合理性检查
        if (abs(packet->diff_seconds) > 86400) { // 不超过一天的差值
            LOG_WARN("[UDPDataConverter]: invalid diff_seconds: %d", packet->diff_seconds);
            return false;
        }
        
        if (abs(packet->diff_nanoseconds) >= 1000000000) { // 纳秒部分不应超过1秒
            LOG_WARN("[UDPDataConverter]: invalid diff_nanoseconds: %d", packet->diff_nanoseconds);
            return false;
        }
        
        return true;
    }
};

} // namespace tmgr 