#pragma once

#include "data_types.h"
#include "iwt_statuscheck.h"
#include <atomic>
#include <memory>
#include <thread>
#include <string>

namespace tmgr {

class UDPTimeChannel {
public:
    UDPTimeChannel(const std::string& server_ip, int server_port,
                   std::shared_ptr<IWTStatusChecker> wt_status_checker);
    ~UDPTimeChannel();
    
    bool init();
    void start();
    void stop();

private:
    void receiveLoop();
    void processValidTimeSource(const WTInfo& info);
    bool createSocket();
    void closeSocket();

private:
    std::string server_ip_;
    int server_port_;
    int socket_fd_;
    std::shared_ptr<IWTStatusChecker> wt_status_checker_;
    
    std::atomic<bool> is_running_;
    std::thread receive_thread_;
    
    static constexpr size_t BUFFER_SIZE = 1024;
    static constexpr int SOCKET_TIMEOUT_SEC = 5;
};

} // namespace tmgr 