#include "someip_time_gap_channel.h"
#include "tmlog.h"
#include <memory>

namespace tmgr {

SomeIPTimeGapChannel::SomeIPTimeGapChannel(
    std::shared_ptr<WTStatusChecker> wt_status_checker)
    : wt_status_checker_(wt_status_checker) {}

SomeIPTimeGapChannel::~SomeIPTimeGapChannel() { proxy_.TimeGap.Unsubscribe(); }

bool SomeIPTimeGapChannel::init() {
  proxy_.TimeGap.SetReceiveHandler([this] {
    proxy_.TimeGap.GetNewSamples(
        [this](const std::shared_ptr<obf::cm::TimeGapData> sample) {
          int64_t timeGapValue = sample->TimeGapValue; // ns
          uint8_t timeGapValueValid = sample->TimeGapValueValid;

          // refer to SOME/IP matrix, 0 == true, 1 == false
          if (timeGapValueValid == 0) {
            static int recv_count = 0;
            if (recv_count++ % 100 ==
                0) { // dump one info every 100 times (20s gap) recving
              if (recv_count == 100) {
                // avoid number overflow
                recv_count = 0;
              }

              LOG_INFO(
                  "[SomeIPTimeGapChannel]: receive time gap event from ICB and "
                  "value is %lld(ns), valid is %d",
                  timeGapValue, timeGapValueValid);
            }

            this->wt_status_checker_->feedTimeGap(timeGapValue);

          } else {
            // if invalid, ignore this event, reset wt status from timeout to
            // normal
            LOG_WARN(
                "[SomeIPTimeGapChannel]: receive time gap event, but it is "
                "**invalid**, will ignore it");

            // even we review invalid values, but we should not treet it as
            // timeout as we have just received it
            this->wt_status_checker_->feedTimeGapDog();
          }
        });
  });

  proxy_.TimeGap.Subscribe(10);

  LOG_INFO("[SomeIPTimeGapChannel]: init proxy / subscribe event success");

  return true;
}

} // namespace tmgr