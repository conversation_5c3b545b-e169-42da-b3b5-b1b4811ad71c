#include "mfr_simulation_node.h"
#include "data_types.h"
#include "mfr/core/subscriber.h"
#include "mfr_node_contants.h"
#include "mtime_mfrmsgs/mtime_mfrmsgs.h"
#include "mtime_mfrmsgs/mfr_cpp_struct_convert.hpp"
#include "tmlog.h"
#include "wt_serializer.h"
#include <chrono>

namespace tmgr {

MFRSimulationNode::MFRSimulationNode() {}

bool MFRSimulationNode::on_init(mfr::MFRNodeHandle *node_handle) {
  mfr::MFRSubscriberConfig sub_config{};

  // currently support three modes, the highest priority is to use time diff
  // 1. Time Diff
  // 2.1 Relative Time check if MTIME_ENABLE_RELATIVE_TIME is ON
  // 2.2 Absolute Time check if MTIME_ENABLE_RELATIVE_TIME is OFF

  // the highest priority is to use time diff
  use_time_diff = []() {
    const char *env_p = std::getenv("MTIME_ENABLE_TIME_DIFF");
    if (env_p != nullptr && std::string(env_p) == "ON") {
      LOG_INFO(
          "[MFRSimulationTimeChannel]: Simulation Time use ***Time Diff*** "
          "Time mode");
      return true;
    }
    return false;
  }();

  if (!use_time_diff) {
    use_enable_relative_time_ = []() {
      const char *env_p = std::getenv("MTIME_ENABLE_RELATIVE_TIME");
      if (env_p != nullptr && std::string(env_p) == "ON") {
        LOG_INFO(
            "[MFRSimulationTimeChannel]: Simulation Time use ***Relative*** "
            "Time mode");
        return true;
      }
      LOG_INFO("[MFRSimulationTimeChannel]: Simulation Time use ***Absolute*** "
               "Time mode");
      return false;
    }();
  }

  if (use_time_diff) {
    subscriber_ = node_handle->communication_manager()
                      .subscribe<mmessage::mtime_mfrmsgs::MFRMessageClock>(
                          mfr::MFRSubscriberConfig{"/clock_diff", 1,
                                                   "time_manager_clock"});
  } else {
    if (use_enable_relative_time_) {
      subscriber_ = node_handle->communication_manager()
                        .subscribe<mmessage::mtime_mfrmsgs::MFRMessageClock>(
                            mfr::MFRSubscriberConfig{"/clock_expect", 1,
                                                     "time_manager_clock"});
    } else {
      subscriber_ =
          node_handle->communication_manager()
              .subscribe<mmessage::mtime_mfrmsgs::MFRMessageClock>(
                  mfr::MFRSubscriberConfig{"/clock", 1, "time_manager_clock"});
    }
  }

  return true;
}
void MFRSimulationNode::on_finish() {}
void MFRSimulationNode::on_running(const mfr::MFRNodeRunningInfo &info) {
  if (info.trigger == mfr::MFR_NODE_TRIGGER_MESSAGE) {

    while (!subscriber_->empty()) {
      auto data = subscriber_->pop<mmessage::mtime_mfrmsgs::MFRMessageClock>();
      maf_mtime::Clock maf_clock_data{};
      mfr_cpp_struct_convert::from_mfr(data, maf_clock_data);
      LOG_DEBUG(
          "[MFRSimulationNode]: Receive simulation clock: %lu, and mode is %s",
          maf_clock_data.clock,
          use_time_diff
              ? "Time Diff"
              : (use_enable_relative_time_ ? "Relative" : "Absolute"));

      if (use_time_diff) {
        WTSerializer::getInstance().setSimulationTimestamp(maf_clock_data.clock);
      } else {
        if (!use_enable_relative_time_) {
          WTSerializer::getInstance().setSimulationTimestamp(maf_clock_data.clock);
        } else {
          uint64_t time_now =
              std::chrono::steady_clock::now().time_since_epoch().count();
          // NOTE: no need to do judgement for these two variables as we use
          // int64 to hold int64_t offset = data.clock() > time_now ?
          // (data.clock() - time_now)
          //                                      : -(time_now - data.clock());
          int64_t offset = maf_clock_data.clock - time_now;
          LOG_DEBUG("[MFRSimulationNode]: clock is %lu - time_now is %lu = "
                    "offset is %ld saved into shared memory",
                    maf_clock_data.clock, time_now, offset);
          WTSerializer::getInstance().setSimulationTimestamp(offset);
        }
      }

      WTSerializer::getInstance().setSimluationTimeMode(
          use_time_diff
              ? SimluationTimeMode::TM_SIMULATION_TIME_DIFF_MODE
              : (use_enable_relative_time_
                     ? SimluationTimeMode::TM_SIMULATION_RELATIVE_TIME_MODE
                     : SimluationTimeMode::TM_SIMULATION_ABSOLUTE_TIME_MODE));
    }
  }
}

static bool MFRSimulationNode_register_value __attribute__((unused)){
    (fprintf(stderr, "[MFRSimulationNode]: Register MFR Node for simulation "
                     "time receiving\n"),
     mfr::MFRNodeFactory::instance().register_by_name(
         MFRNodeConstants::node_type,
         []() { return mmemory::MFMakeShared<MFRSimulationNode>(); }))};
// MFR_REGISTER_NODE(MFRSimulationNode, MFRNodeConstants::node_type);

} // namespace tmgr