#include "http_time_channel.h"
#include "data_types.h"
#include "http_data_coverter.hpp"
#include "httplib.h"
#include "iwt_statuscheck.h"
#include "system_clock_utility.h"
#include "tmlog.h"
#include <chrono>
#include <cstdint>
#include <ctime>
#include <iomanip>
#include <sstream>

namespace tmgr {
HTTPTimeChannel::HTTPTimeChannel(
    const std::string &url,
    std::shared_ptr<IWTStatusChecker> iwt_status_checker)
    : cli(url.c_str()), url_(url), iwt_status_checker_(iwt_status_checker),
      server_time_stamp_(0) {

  LOG_INFO("[HTTPTimeChannel]: HTTPTimeChannel begin to get timestamp from "
           "url: %s...",
           url.c_str());
  init();
}

void HTTPTimeChannel::init() {
  // TODO: maybe this can add retry times for robustness
  // NOTE: maybe this thread will be competed with someip callback thread,
  // should be careful, maybe can add some delay for next actions
  std::thread([this]() {
    bool is_get_timestamp = getTimeStampFromServer();
    if (is_get_timestamp && server_time_stamp_ != 0) {
      WTInfo info = HttpDataConverter::convertToWTInfo(server_time_stamp_);
      syncWTInfo(info);
    } else {
      LOG_ERR("[HTTPTimeChannel]: Failed to get timestamp from server, should "
              "be check if internet is OK.");
    };
  })
      .detach();
}

bool HTTPTimeChannel::getTimeStampFromServer() {
  auto res = cli.Get("/");
  if (res && res->status == 200) {
    date_str_ = res->get_header_value("Date");
    LOG_INFO("[HTTPTimeChannel]: Receive Data Header from %s is %s",
             url_.c_str(), date_str_.c_str());

    std::istringstream date_stream(date_str_);
    std::tm tm = {};
    date_stream >>
        std::get_time(&tm, "%a, %d %b %Y %H:%M:%S GMT"); // 解析日期格式

    if (date_stream.fail()) {
      LOG_ERR("[HTTPTimeChannel]: Failed to parse Date header: %s.",
              date_str_.c_str());
    } else {
      // 注意：std::mktime
      // 默认按本地时区转换，对于GMT时间，需要手动调整或使用其他方法
      auto time_c = timegm(&tm); // 使用timegm代替mktime来正确处理GMT/UTC时间
      if (time_c == static_cast<time_t>(-1)) {
        LOG_ERR(
            "[HTTPTimeChannel]: Conversion to time_t failed with number %lld.",
            time_c);
      } else {
        auto time_point = std::chrono::system_clock::from_time_t(time_c);
        auto nanoseconds = std::chrono::duration_cast<std::chrono::nanoseconds>(
            time_point.time_since_epoch());

        LOG_INFO("[HTTPTimeChannel]: generate timestamp with nanoseconds "
                 "since epoch: %lld, GMT time is %s",
                 nanoseconds.count(),
                 SystemClockUtility::prettyStrTimePoint(Timestamp(nanoseconds))
                     .c_str());
        server_time_stamp_ = nanoseconds.count();
        return true;
      }
    }
    return false; // TODO: try to comment this line to see if compiler warning
                  // as error
  } else {
    LOG_ERR("[HTTPTimeChannel]: Internet service is offline, failed to get "
            "response or date header missing.");
    return false;
  }
}

void HTTPTimeChannel::syncWTInfo(const WTInfo &info) {
  LOG_INFO(
      "[HTTPTimeChannel]: feed wtinfo with values: diff_seconds: %d, "
      "diff_nanoseconds: %d, wt_sync_status: %d, ot_master_sync_status: %d",
      info.diff_seconds.load(), info.diff_nanoseconds.load(),
      info.wt_sync_status.load(), info.ot_master_sync_status.load());
  iwt_status_checker_->feedWTInfo(info);
}

int64_t HTTPTimeChannel::timeStampNanoSecCount() const {
  return server_time_stamp_;
}

std::string HTTPTimeChannel::serverDateResponse() const { return date_str_; }

}; // namespace tmgr
