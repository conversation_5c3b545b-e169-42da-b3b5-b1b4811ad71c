#pragma once

#include "data_types.h"
#include "httplib.h"
#include <cstdint>

namespace tmgr {

class IWTStatusChecker;

class HTTPTimeChannel {
public:
  HTTPTimeChannel(const std::string &url,
                  std::shared_ptr<IWTStatusChecker> iwt_status_checker);
  std::string serverDateResponse() const;
  int64_t timeStampNanoSecCount() const;

private:
  void init();
  bool getTimeStampFromServer();
  void syncWTInfo(const WTInfo &info);

private:
  httplib::Client cli;
  std::string url_;
  std::shared_ptr<IWTStatusChecker> iwt_status_checker_;
  int64_t server_time_stamp_;
  std::string date_str_;
};
} // namespace tmgr