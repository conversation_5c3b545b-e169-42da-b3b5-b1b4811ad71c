#pragma once

#include "data_types.h"
#include "mmemory/advanced_types.h"

namespace tmgr {
class MFRSimulationTimeChannel {
public:
  MFRSimulationTimeChannel(RunningMode mode);
  ~MFRSimulationTimeChannel();
  void init();

private:
  void initMFRTools();
  void registerNodes();
  void initMachine(const mmemory::MFString &machine_url);
  void resetSimulationTime();

private:
  RunningMode mode_{RunningMode::TM_NORMAL_MODE};
};
} // namespace tmgr