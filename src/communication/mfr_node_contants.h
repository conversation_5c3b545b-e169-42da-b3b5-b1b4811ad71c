#pragma once

#include <cstdlib>

namespace tmgr {

struct MFRNodeConstants {
  static constexpr auto node_yaml = R"(
node_config:
  memory:
    total_virtual_memory_size_MB: 0
    total_shared_memory_size_MB: 0
)";

  static constexpr auto machine_yaml = R"(
log:
  level: info
  file:
    file_name: ''
    file_type: write
    max_file_size: 0
    max_file_num: 1
  enable_stderr: true
  export_frequence: 0
)";

  static constexpr auto node_type = "timemanager_node_type";
  static constexpr auto node_name = "timemanager_node_name";
  static constexpr auto machine_name = "timemanager_machine_name";
  static constexpr auto default_master_uri = "mfrrpc://192.168.1.1:11300";
};

} // namespace tmgr
