#include "udp_time_channel.h"
#include "udp_data_converter.hpp"
#include "tmlog.h"
#include "wt_serializer.h"
#ifdef TM_USE_SYSTEM_TIME
#include "system_clock_manager.h"
#endif

#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cerrno>
#include <cstring>

namespace tmgr {

UDPTimeChannel::UDPTimeChannel(const std::string& server_ip, int server_port,
                               std::shared_ptr<IWTStatusChecker> wt_status_checker)
    : server_ip_(server_ip), server_port_(server_port), socket_fd_(-1),
      wt_status_checker_(wt_status_checker), is_running_(false) {
    LOG_INFO("[UDPTimeChannel]: UDPTimeChannel initialized for %s:%d", 
             server_ip_.c_str(), server_port_);
}

UDPTimeChannel::~UDPTimeChannel() {
    stop();
}

bool UDPTimeChannel::init() {
    if (!createSocket()) {
        LOG_ERR("[UDPTimeChannel]: Failed to create UDP socket");
        return false;
    }
    
    LOG_INFO("[UDPTimeChannel]: UDP socket created successfully");
    return true;
}

void UDPTimeChannel::start() {
    if (is_running_.load()) {
        LOG_WARN("[UDPTimeChannel]: UDP channel already running");
        return;
    }
    
    is_running_.store(true);
    receive_thread_ = std::thread(&UDPTimeChannel::receiveLoop, this);
    
    LOG_INFO("[UDPTimeChannel]: UDP receive thread started");
}

void UDPTimeChannel::stop() {
    if (!is_running_.load()) {
        return;
    }
    
    is_running_.store(false);
    
    if (receive_thread_.joinable()) {
        receive_thread_.join();
    }
    
    closeSocket();
    LOG_INFO("[UDPTimeChannel]: UDP channel stopped");
}

bool UDPTimeChannel::createSocket() {
    socket_fd_ = socket(AF_INET, SOCK_DGRAM, 0);
    if (socket_fd_ < 0) {
        LOG_ERR("[UDPTimeChannel]: Failed to create socket: %s", strerror(errno));
        return false;
    }
    
    // 设置套接字选项
    int reuse = 1;
    if (setsockopt(socket_fd_, SOL_SOCKET, SO_REUSEADDR, &reuse, sizeof(reuse)) < 0) {
        LOG_WARN("[UDPTimeChannel]: Failed to set SO_REUSEADDR: %s", strerror(errno));
    }
    
    // 设置接收超时
    struct timeval timeout;
    timeout.tv_sec = SOCKET_TIMEOUT_SEC;
    timeout.tv_usec = 0;
    if (setsockopt(socket_fd_, SOL_SOCKET, SO_RCVTIMEO, &timeout, sizeof(timeout)) < 0) {
        LOG_WARN("[UDPTimeChannel]: Failed to set socket timeout: %s", strerror(errno));
    }
    
    // 绑定到指定地址和端口
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(server_port_);
    
    if (inet_pton(AF_INET, server_ip_.c_str(), &server_addr.sin_addr) <= 0) {
        LOG_ERR("[UDPTimeChannel]: Invalid IP address: %s", server_ip_.c_str());
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }
    
    if (bind(socket_fd_, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        LOG_ERR("[UDPTimeChannel]: Failed to bind socket to %s:%d: %s", 
                server_ip_.c_str(), server_port_, strerror(errno));
        close(socket_fd_);
        socket_fd_ = -1;
        return false;
    }
    
    LOG_INFO("[UDPTimeChannel]: Socket bound to %s:%d", server_ip_.c_str(), server_port_);
    return true;
}

void UDPTimeChannel::closeSocket() {
    if (socket_fd_ >= 0) {
        close(socket_fd_);
        socket_fd_ = -1;
    }
}

void UDPTimeChannel::receiveLoop() {
    uint8_t buffer[BUFFER_SIZE];
    struct sockaddr_in client_addr;
    socklen_t client_len = sizeof(client_addr);
    
    LOG_INFO("[UDPTimeChannel]: Starting UDP receive loop");
    
    while (is_running_.load()) {
        ssize_t received = recvfrom(socket_fd_, buffer, sizeof(buffer), 0,
                                   (struct sockaddr*)&client_addr, &client_len);
        
        if (received < 0) {
            if (errno == EAGAIN || errno == EWOULDBLOCK) {
                // 超时，继续循环
                continue;
            } else {
                LOG_ERR("[UDPTimeChannel]: recvfrom failed: %s", strerror(errno));
                break;
            }
        }
        
        if (received == 0) {
            LOG_WARN("[UDPTimeChannel]: Received empty packet");
            continue;
        }
        
        // 记录来源地址
        char client_ip[INET_ADDRSTRLEN];
        inet_ntop(AF_INET, &client_addr.sin_addr, client_ip, INET_ADDRSTRLEN);
        
        LOG_DEBUG("[UDPTimeChannel]: Received %zd bytes from %s:%d", 
                  received, client_ip, ntohs(client_addr.sin_port));
        
        // 验证并处理数据包
        if (!UDPDataConverter::validatePacket(buffer, received)) {
            LOG_WARN("[UDPTimeChannel]: Invalid packet received from %s:%d", 
                     client_ip, ntohs(client_addr.sin_port));
            continue;
        }
        
        const UDPDiffPacket* packet = reinterpret_cast<const UDPDiffPacket*>(buffer);
        WTInfo info = UDPDataConverter::convertToWTInfo(*packet);
        
        LOG_INFO("[UDPTimeChannel]: Received valid diff data from %s:%d - "
                 "diff_seconds=%d, diff_nanoseconds=%d, ot_sync_status=%d",
                 client_ip, ntohs(client_addr.sin_port),
                 info.diff_seconds.load(), info.diff_nanoseconds.load(),
                 static_cast<int>(info.ot_master_sync_status.load()));
        
        // 处理有效的时间源数据
        processValidTimeSource(info);
        
        // 通知状态检查器收到了数据
        if (wt_status_checker_) {
            wt_status_checker_->feedWTInfoDog();
        }
    }
    
    LOG_INFO("[UDPTimeChannel]: UDP receive loop finished");
}

void UDPTimeChannel::processValidTimeSource(const WTInfo& info) {
    int64_t total_diff_nanoseconds = 
        info.diff_seconds * 1000000000LL + info.diff_nanoseconds;
    
    auto timeGap = WTSerializer::getInstance().timeGap();
    auto wtSyncMode = WTSerializer::getInstance().wtSyncMode();
    
    LOG_INFO("[UDPTimeChannel]: wtSyncMode is %s, add "
             "total_diff_nanoseconds: %lld(ns) with gap value(normal "
             "mode will ignore it) %lld(ns) to %lld(ns)",
             wtSyncMode == WTSyncMode::WT_SYNC_MODE_NORMAL ? "Normal" : "Gap",
             total_diff_nanoseconds, timeGap, total_diff_nanoseconds + timeGap);
    
    if (wtSyncMode == WTSyncMode::WT_SYNC_MODE_GAP) {
        total_diff_nanoseconds += timeGap;
    }
    
    // 如果启用了系统时间管理，这里可以添加系统时间更新逻辑
    // 类似于SomeIP时间通道的处理方式
    
    // 将WTInfo传递给状态检查器
    if (wt_status_checker_) {
        wt_status_checker_->feedWTInfo(info);
    }
}

} // namespace tmgr 