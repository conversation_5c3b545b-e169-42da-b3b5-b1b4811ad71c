#pragma once

#include "data_types.h"
#include "iwt_statuscheck.h"
#include <atomic>
#include <memory>
#include <thread>
#include <string>
#include <functional>

// Forward declarations for MQTT client
struct mosquitto;

namespace tmgr {

/**
 * MQTT时间通道类
 * 负责通过MQTT协议接收时间差报文并转换为WTInfo
 */
class MQTTTimeChannel {
public:
    /**
     * 构造函数
     * @param broker_host MQTT代理服务器地址
     * @param broker_port MQTT代理服务器端口
     * @param topic MQTT主题
     * @param client_id MQTT客户端ID
     * @param wt_status_checker 状态检查器
     */
    MQTTTimeChannel(const std::string& broker_host, 
                    int broker_port,
                    const std::string& topic,
                    const std::string& client_id,
                    std::shared_ptr<IWTStatusChecker> wt_status_checker);
    
    /**
     * 析构函数
     */
    ~MQTTTimeChannel();
    
    /**
     * 初始化MQTT连接
     * @return 成功返回true，失败返回false
     */
    bool init();
    
    /**
     * 启动MQTT客户端
     */
    void start();
    
    /**
     * 停止MQTT客户端
     */
    void stop();
    
    /**
     * 检查连接状态
     * @return 连接状态
     */
    bool isConnected() const;

private:
    /**
     * MQTT连接回调
     */
    static void onConnect(struct mosquitto* mosq, void* userdata, int result);
    
    /**
     * MQTT断开连接回调
     */
    static void onDisconnect(struct mosquitto* mosq, void* userdata, int result);
    
    /**
     * MQTT消息接收回调
     */
    static void onMessage(struct mosquitto* mosq, void* userdata, 
                         const struct mosquitto_message* message);
    
    /**
     * MQTT日志回调
     */
    static void onLog(struct mosquitto* mosq, void* userdata, int level, const char* str);
    
    /**
     * 处理接收到的MQTT消息
     */
    void handleMessage(const std::string& payload);
    
    /**
     * 处理有效的时间源数据
     */
    void processValidTimeSource(const WTInfo& info);
    
    /**
     * 重连逻辑
     */
    void reconnectLoop();
    
    /**
     * 清理资源
     */
    void cleanup();

private:
    // MQTT配置参数
    std::string broker_host_;
    int broker_port_;
    std::string topic_;
    std::string client_id_;
    
    // MQTT客户端实例
    struct mosquitto* mosq_;
    
    // 状态检查器
    std::shared_ptr<IWTStatusChecker> wt_status_checker_;
    
    // 运行状态
    std::atomic<bool> is_running_;
    std::atomic<bool> is_connected_;
    
    // 重连线程
    std::thread reconnect_thread_;
    
    // 统计信息
    std::atomic<uint64_t> messages_received_;
    std::atomic<uint64_t> messages_processed_;
    std::atomic<uint64_t> connection_attempts_;
    
    // 常量配置
    static constexpr int KEEP_ALIVE_SECONDS = 60;
    static constexpr int RECONNECT_DELAY_MS = 5000;
    static constexpr int QOS_LEVEL = 1;
    static constexpr int MAX_RECONNECT_ATTEMPTS = 10;
};

} // namespace tmgr
