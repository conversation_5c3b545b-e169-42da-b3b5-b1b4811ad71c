#include "mqtt_time_channel.h"
#include "mqtt_data_converter.hpp"
#include "tmlog.h"
#include "wt_serializer.h"
#include <mosquitto.h>
#include <chrono>
#include <thread>
#include <cstring>

namespace tmgr {

MQTTTimeChannel::MQTTTimeChannel(const std::string& broker_host, 
                                 int broker_port,
                                 const std::string& topic,
                                 const std::string& client_id,
                                 std::shared_ptr<IWTStatusChecker> wt_status_checker)
    : broker_host_(broker_host), broker_port_(broker_port), topic_(topic), 
      client_id_(client_id), mosq_(nullptr), wt_status_checker_(wt_status_checker),
      is_running_(false), is_connected_(false), messages_received_(0),
      messages_processed_(0), connection_attempts_(0) {
    
    LOG_INFO("[MQTTTimeChannel]: MQTTTimeChannel initialized for %s:%d, topic: %s, client_id: %s", 
             broker_host_.c_str(), broker_port_, topic_.c_str(), client_id_.c_str());
}

MQTTTimeChannel::~MQTTTimeChannel() {
    stop();
    cleanup();
}

bool MQTTTimeChannel::init() {
    // 初始化mosquitto库
    int result = mosquitto_lib_init();
    if (result != MOSQ_ERR_SUCCESS) {
        LOG_ERR("[MQTTTimeChannel]: Failed to initialize mosquitto library: %s", 
                mosquitto_strerror(result));
        return false;
    }
    
    // 创建mosquitto客户端实例
    mosq_ = mosquitto_new(client_id_.c_str(), true, this);
    if (!mosq_) {
        LOG_ERR("[MQTTTimeChannel]: Failed to create mosquitto client");
        mosquitto_lib_cleanup();
        return false;
    }
    
    // 设置回调函数
    mosquitto_connect_callback_set(mosq_, onConnect);
    mosquitto_disconnect_callback_set(mosq_, onDisconnect);
    mosquitto_message_callback_set(mosq_, onMessage);
    mosquitto_log_callback_set(mosq_, onLog);
    
    LOG_INFO("[MQTTTimeChannel]: MQTT client initialized successfully");
    return true;
}

void MQTTTimeChannel::start() {
    if (is_running_.load()) {
        LOG_WARN("[MQTTTimeChannel]: MQTT channel already running");
        return;
    }
    
    is_running_.store(true);
    
    // 启动重连线程
    reconnect_thread_ = std::thread(&MQTTTimeChannel::reconnectLoop, this);
    
    LOG_INFO("[MQTTTimeChannel]: MQTT channel started");
}

void MQTTTimeChannel::stop() {
    if (!is_running_.load()) {
        return;
    }
    
    is_running_.store(false);
    is_connected_.store(false);
    
    // 断开MQTT连接
    if (mosq_) {
        mosquitto_disconnect(mosq_);
    }
    
    // 等待重连线程结束
    if (reconnect_thread_.joinable()) {
        reconnect_thread_.join();
    }
    
    LOG_INFO("[MQTTTimeChannel]: MQTT channel stopped. Stats - Received: %lu, Processed: %lu, Attempts: %lu",
             messages_received_.load(), messages_processed_.load(), connection_attempts_.load());
}

bool MQTTTimeChannel::isConnected() const {
    return is_connected_.load();
}

void MQTTTimeChannel::onConnect(struct mosquitto* mosq, void* userdata, int result) {
    MQTTTimeChannel* channel = static_cast<MQTTTimeChannel*>(userdata);
    
    if (result == 0) {
        channel->is_connected_.store(true);
        LOG_INFO("[MQTTTimeChannel]: Connected to MQTT broker successfully");
        
        // 订阅主题
        int sub_result = mosquitto_subscribe(mosq, nullptr, channel->topic_.c_str(), QOS_LEVEL);
        if (sub_result != MOSQ_ERR_SUCCESS) {
            LOG_ERR("[MQTTTimeChannel]: Failed to subscribe to topic %s: %s", 
                    channel->topic_.c_str(), mosquitto_strerror(sub_result));
        } else {
            LOG_INFO("[MQTTTimeChannel]: Subscribed to topic: %s", channel->topic_.c_str());
        }
    } else {
        LOG_ERR("[MQTTTimeChannel]: Failed to connect to MQTT broker: %s", 
                mosquitto_connack_string(result));
        channel->is_connected_.store(false);
    }
}

void MQTTTimeChannel::onDisconnect(struct mosquitto* mosq, void* userdata, int result) {
    MQTTTimeChannel* channel = static_cast<MQTTTimeChannel*>(userdata);
    channel->is_connected_.store(false);
    
    if (result == 0) {
        LOG_INFO("[MQTTTimeChannel]: Disconnected from MQTT broker gracefully");
    } else {
        LOG_WARN("[MQTTTimeChannel]: Unexpected disconnection from MQTT broker: %s", 
                 mosquitto_strerror(result));
    }
}

void MQTTTimeChannel::onMessage(struct mosquitto* mosq, void* userdata, 
                                const struct mosquitto_message* message) {
    MQTTTimeChannel* channel = static_cast<MQTTTimeChannel*>(userdata);
    channel->messages_received_.fetch_add(1);
    
    if (message->payloadlen > 0 && message->payload) {
        std::string payload(static_cast<const char*>(message->payload), message->payloadlen);
        
        LOG_DEBUG("[MQTTTimeChannel]: Received message on topic %s, payload size: %d", 
                  message->topic, message->payloadlen);
        
        channel->handleMessage(payload);
    } else {
        LOG_WARN("[MQTTTimeChannel]: Received empty message on topic %s", message->topic);
    }
}

void MQTTTimeChannel::onLog(struct mosquitto* mosq, void* userdata, int level, const char* str) {
    // 根据mosquitto日志级别映射到系统日志级别
    switch (level) {
        case MOSQ_LOG_DEBUG:
            LOG_DEBUG("[MQTTTimeChannel-Mosquitto]: %s", str);
            break;
        case MOSQ_LOG_INFO:
            LOG_INFO("[MQTTTimeChannel-Mosquitto]: %s", str);
            break;
        case MOSQ_LOG_NOTICE:
            LOG_INFO("[MQTTTimeChannel-Mosquitto]: %s", str);
            break;
        case MOSQ_LOG_WARNING:
            LOG_WARN("[MQTTTimeChannel-Mosquitto]: %s", str);
            break;
        case MOSQ_LOG_ERR:
            LOG_ERR("[MQTTTimeChannel-Mosquitto]: %s", str);
            break;
        default:
            LOG_INFO("[MQTTTimeChannel-Mosquitto]: %s", str);
            break;
    }
}

void MQTTTimeChannel::handleMessage(const std::string& payload) {
    try {
        // 自动检测消息格式
        MQTTMessageFormat format = MQTTDataConverter::detectMessageFormat(payload);
        
        // 转换为WTInfo
        WTInfo info = MQTTDataConverter::convertToWTInfo(payload, format);
        
        // 验证数据有效性
        if (MQTTDataConverter::validateWTInfo(info)) {
            processValidTimeSource(info);
            messages_processed_.fetch_add(1);
            
            LOG_DEBUG("[MQTTTimeChannel]: Successfully processed MQTT message - "
                     "diff_seconds=%d, diff_nanoseconds=%d, ot_sync_status=%d",
                     info.diff_seconds.load(), info.diff_nanoseconds.load(),
                     static_cast<int>(info.ot_master_sync_status.load()));
        } else {
            LOG_WARN("[MQTTTimeChannel]: Invalid WTInfo data received, ignoring message");
        }
    } catch (const std::exception& e) {
        LOG_ERR("[MQTTTimeChannel]: Exception while processing MQTT message: %s", e.what());
    }
}

void MQTTTimeChannel::processValidTimeSource(const WTInfo& info) {
    // 将时间信息传递给状态检查器
    if (wt_status_checker_) {
        wt_status_checker_->feedWTInfo(info);
        wt_status_checker_->feedWTInfoDog();
    }
    
    // 序列化到共享内存
    WTSerializer::getInstance().setWTInfo(info);
    
    LOG_INFO("[MQTTTimeChannel]: Processed valid time source - "
             "diff_seconds=%d, diff_nanoseconds=%d, wt_sync_status=%d, ot_master_sync_status=%d",
             info.diff_seconds.load(), info.diff_nanoseconds.load(),
             info.wt_sync_status.load(), info.ot_master_sync_status.load());
}

void MQTTTimeChannel::reconnectLoop() {
    while (is_running_.load()) {
        if (!is_connected_.load()) {
            connection_attempts_.fetch_add(1);
            
            LOG_INFO("[MQTTTimeChannel]: Attempting to connect to MQTT broker %s:%d (attempt %lu)",
                     broker_host_.c_str(), broker_port_, connection_attempts_.load());
            
            int result = mosquitto_connect(mosq_, broker_host_.c_str(), broker_port_, KEEP_ALIVE_SECONDS);
            if (result == MOSQ_ERR_SUCCESS) {
                // 启动网络循环
                mosquitto_loop_start(mosq_);
            } else {
                LOG_ERR("[MQTTTimeChannel]: Failed to connect to MQTT broker: %s", 
                        mosquitto_strerror(result));
                
                // 等待重连
                std::this_thread::sleep_for(std::chrono::milliseconds(RECONNECT_DELAY_MS));
            }
        } else {
            // 已连接，等待一段时间再检查
            std::this_thread::sleep_for(std::chrono::milliseconds(1000));
        }
    }
    
    // 停止网络循环
    if (mosq_) {
        mosquitto_loop_stop(mosq_, true);
    }
}

void MQTTTimeChannel::cleanup() {
    if (mosq_) {
        mosquitto_destroy(mosq_);
        mosq_ = nullptr;
    }
    
    mosquitto_lib_cleanup();
    LOG_INFO("[MQTTTimeChannel]: Cleanup completed");
}

} // namespace tmgr
