#include "mfr_simulation_time_channel.h"
#include "data_types.h"
#include "mfr/core/node.h"
#include "mfr/mfr.h"
#include "mfr_node_contants.h"
#include "tmlog.h"
#include "wt_serializer.h"

namespace tmgr {
MFRSimulationTimeChannel::MFRSimulationTimeChannel(RunningMode mode) {
  mode_ = mode;
  init();
}
void MFRSimulationTimeChannel::init() {
  initMFRTools();
  resetSimulationTime();
}

MFRSimulationTimeChannel::~MFRSimulationTimeChannel() {
  mfr::MFRNodeMachine::instance().stop();
  mfr::MFRNodeMachine::instance().join();
  (void)mfr::MFRNodeMachine::instance().reset();
  resetSimulationTime();
}

void MFRSimulationTimeChannel::initMFRTools() {
  // users should set MFR_DISTRIBUTED_MODE=1 in the environment
  // char *distributed_mode = getenv("MFR_DISTRIBUTED_MODE");
  // if (distributed_mode == nullptr) {
  //  (void)setenv("MFR_DISTRIBUTED_MODE", "1", 1);
  //}

  mmemory::MFString machine_url;
  if (const char *env{std::getenv("MFR_MASTER_URI")}) {
    machine_url = env;
  } else {
    machine_url = MFRNodeConstants::default_master_uri;
  }

  // only simulation mode need to register nodes, otherwise this channel's
  // responsibility is to init mfr channels
  if (mode_ == RunningMode::TM_SIMULATION_MODE) {
    registerNodes();
  }

  initMachine(machine_url);
  mfr::MFRNodeMachine::instance().run();
}

void MFRSimulationTimeChannel::registerNodes() {
  std::string node_name{MFRNodeConstants::node_name};

  mfr::MFRNodeConfig node_config{};
  node_config.node_type = MFRNodeConstants::node_type;
  node_config.node_name = node_name.c_str();
  node_config.node_param_yaml = MFRNodeConstants::node_yaml;
  mfr::MFRNodeMachine::instance().register_node(node_config);
}

void MFRSimulationTimeChannel::initMachine(
    const mmemory::MFString &machine_url) {
  std::string machine_name{MFRNodeConstants::machine_name};

  mfr::MFRMachineConfig machine_config{};
  machine_config.machine_url = machine_url;
  machine_config.machine_name = machine_name.c_str();
  machine_config.machine_param_yaml = MFRNodeConstants::machine_yaml;
  (void)mfr::MFRNodeMachine::instance().init(machine_config);
}

void MFRSimulationTimeChannel::resetSimulationTime() {
  WTSerializer::getInstance().setSimluationTimeMode(
      SimluationTimeMode::TM_SIMULATION_UNINITIALIZED);
  WTSerializer::getInstance().setSimulationTimestamp(0);
}

} // namespace tmgr