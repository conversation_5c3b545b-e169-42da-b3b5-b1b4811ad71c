#pragma once

#include "data_types.h"
#include "ot_consumer.h"
#include "tmlog.h"
#include <cstdint>
#include <cstring>
#include <string>

namespace tmgr {

/**
 * MQTT消息格式定义
 * 使用二进制格式，紧凑高效
 */
struct MQTTDiffPacket {
    int32_t diff_seconds;      // 时间差秒数
    int32_t diff_nanoseconds;  // 时间差纳秒数
    uint8_t ot_sync_status;    // OT同步状态
    uint8_t reserved[3];       // 对齐填充
    int64_t timestamp;         // 消息时间戳
    char source_id[16];        // 消息源标识
} __attribute__((packed));

struct MQTTDataConverter {
public:
    /**
     * 从MQTT消息转换为WTInfo
     */
    static WTInfo convertToWTInfo(const MQTTDiffPacket& packet) {
        WTInfo info{};

        // 直接使用MQTT消息中的diff数据
        info.diff_seconds = packet.diff_seconds;
        info.diff_nanoseconds = packet.diff_nanoseconds;
        info.ot_master_sync_status = static_cast<OTMasterSyncStatus>(packet.ot_sync_status);
        info.wt_sync_status = WT_STATE_SYNCHRONIZED; // MQTT数据源认为是同步的

        LOG_INFO("[MQTTDataConverter]: converted MQTT packet to WTInfo: "
                 "diff_seconds=%d, diff_nanoseconds=%d, ot_sync_status=%d, source_id=%.16s",
                 info.diff_seconds.load(), info.diff_nanoseconds.load(),
                 static_cast<int>(info.ot_master_sync_status.load()), packet.source_id);

        return info;
    }
    
    /**
     * 验证二进制MQTT数据包
     */
    static bool validateBinaryPacket(const void* data, size_t length) {
        if (length < sizeof(MQTTDiffPacket)) {
            LOG_WARN("[MQTTDataConverter]: binary packet too small, expected %zu bytes, got %zu",
                     sizeof(MQTTDiffPacket), length);
            return false;
        }
        
        const MQTTDiffPacket* packet = static_cast<const MQTTDiffPacket*>(data);
        
        // 基本的数据合理性检查
        if (abs(packet->diff_seconds) > 86400) { // 不超过一天的差值
            LOG_WARN("[MQTTDataConverter]: invalid diff_seconds: %d", packet->diff_seconds);
            return false;
        }
        
        if (abs(packet->diff_nanoseconds) >= 1000000000) { // 纳秒部分不应超过1秒
            LOG_WARN("[MQTTDataConverter]: invalid diff_nanoseconds: %d", packet->diff_nanoseconds);
            return false;
        }
        
        // 检查OT同步状态的有效性
        if (packet->ot_sync_status > static_cast<uint8_t>(OT_MASTER_SYNCHRONIZED)) {
            LOG_WARN("[MQTTDataConverter]: invalid ot_sync_status: %d", packet->ot_sync_status);
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证JSON MQTT消息
     */
    static bool validateJsonMessage(const std::string& json_str) {
        try {
            nlohmann::json j = nlohmann::json::parse(json_str);
            
            // 检查必需字段
            if (!j.contains("diff_seconds") || !j.contains("diff_nanoseconds") || 
                !j.contains("ot_sync_status")) {
                LOG_WARN("[MQTTDataConverter]: JSON message missing required fields");
                return false;
            }
            
            int32_t diff_seconds = j.at("diff_seconds").get<int32_t>();
            int32_t diff_nanoseconds = j.at("diff_nanoseconds").get<int32_t>();
            uint8_t ot_sync_status = j.at("ot_sync_status").get<uint8_t>();
            
            // 数据合理性检查
            if (abs(diff_seconds) > 86400) {
                LOG_WARN("[MQTTDataConverter]: invalid diff_seconds in JSON: %d", diff_seconds);
                return false;
            }
            
            if (abs(diff_nanoseconds) >= 1000000000) {
                LOG_WARN("[MQTTDataConverter]: invalid diff_nanoseconds in JSON: %d", diff_nanoseconds);
                return false;
            }
            
            if (ot_sync_status > static_cast<uint8_t>(OT_MASTER_SYNCHRONIZED)) {
                LOG_WARN("[MQTTDataConverter]: invalid ot_sync_status in JSON: %d", ot_sync_status);
                return false;
            }
            
            return true;
            
        } catch (const nlohmann::json::exception& e) {
            LOG_WARN("[MQTTDataConverter]: JSON validation failed: %s", e.what());
            return false;
        }
    }
    
    /**
     * 自动检测消息格式并转换
     */
    static WTInfo convertToWTInfo(const std::string& message, MQTTMessageFormat format) {
        if (format == MQTTMessageFormat::JSON) {
            return convertJsonToWTInfo(message);
        } else {
            // 二进制格式
            if (message.size() >= sizeof(MQTTDiffPacket)) {
                const MQTTDiffPacket* packet = 
                    reinterpret_cast<const MQTTDiffPacket*>(message.data());
                return convertBinaryToWTInfo(*packet);
            } else {
                LOG_ERR("[MQTTDataConverter]: Binary message too small");
                WTInfo info{};
                info.wt_sync_status = WT_STATE_TIMEOUT;
                info.ot_master_sync_status = OT_MASTER_NOT_SYNCHRONIZED;
                return info;
            }
        }
    }
    
    /**
     * 自动检测消息格式
     */
    static MQTTMessageFormat detectMessageFormat(const std::string& message) {
        // 简单的格式检测：如果以'{'开头，认为是JSON
        if (!message.empty() && message[0] == '{') {
            return MQTTMessageFormat::JSON;
        }
        return MQTTMessageFormat::BINARY;
    }
};

} // namespace tmgr
