#pragma once

#include "data_types.h"
#include "ot_consumer.h"
#include "tmlog.h"
#include <cstdint>
#include <cstring>
#include <string>
#include <chrono>
#include <stdexcept>

namespace tmgr {

/**
 * MQTT消息格式定义
 * 使用二进制格式，紧凑高效
 */
struct MQTTDiffPacket {
    int32_t diff_seconds;      // 时间差秒数
    int32_t diff_nanoseconds;  // 时间差纳秒数
    uint8_t ot_sync_status;    // OT同步状态
    uint8_t reserved[3];       // 对齐填充
    int64_t timestamp;         // 消息时间戳
    char source_id[16];        // 消息源标识
} __attribute__((packed));

/**
 * MQTT消息格式枚举
 */
enum class MQTTMessageFormat {
    BINARY,  // 二进制格式
    JSON     // JSON格式
};

struct MQTTDataConverter {
public:
    /**
     * 从MQTT二进制消息转换为WTInfo
     */
    static WTInfo convertBinaryToWTInfo(const MQTTDiffPacket& packet) {
        WTInfo info{};

        // 直接使用MQTT消息中的diff数据
        info.diff_seconds = packet.diff_seconds;
        info.diff_nanoseconds = packet.diff_nanoseconds;
        info.ot_master_sync_status = static_cast<OTMasterSyncStatus>(packet.ot_sync_status);
        info.wt_sync_status = WT_STATE_SYNCHRONIZED; // MQTT数据源认为是同步的

        LOG_INFO("[MQTTDataConverter]: converted MQTT binary packet to WTInfo: "
                 "diff_seconds=%d, diff_nanoseconds=%d, ot_sync_status=%d, source_id=%.16s",
                 info.diff_seconds.load(), info.diff_nanoseconds.load(),
                 static_cast<int>(info.ot_master_sync_status.load()), packet.source_id);

        return info;
    }

    /**
     * 验证MQTT消息的有效性
     */
    static bool validatePacket(const MQTTDiffPacket& packet) {
        // 检查时间差的合理性（例如：不超过24小时）
        const int32_t MAX_DIFF_SECONDS = 24 * 3600; // 24小时
        if (abs(packet.diff_seconds) > MAX_DIFF_SECONDS) {
            LOG_WARN("[MQTTDataConverter]: diff_seconds out of range: %d", packet.diff_seconds);
            return false;
        }
        
        // 检查纳秒数的有效性
        if (abs(packet.diff_nanoseconds) >= 1000000000) {
            LOG_WARN("[MQTTDataConverter]: diff_nanoseconds out of range: %d", packet.diff_nanoseconds);
            return false;
        }
        
        // 检查OT同步状态的有效性
        if (packet.ot_sync_status > static_cast<uint8_t>(OT_MASTER_SYNCHRONIZED)) {
            LOG_WARN("[MQTTDataConverter]: invalid ot_sync_status: %d", packet.ot_sync_status);
            return false;
        }
        
        // 检查时间戳的合理性（不能是未来时间，不能太旧）
        auto now = std::chrono::system_clock::now();
        auto now_ns = std::chrono::duration_cast<std::chrono::nanoseconds>(now.time_since_epoch()).count();
        
        // 允许5分钟的时间偏差
        const int64_t MAX_TIME_DIFF_NS = 5 * 60 * 1000000000LL; // 5分钟
        if (abs(packet.timestamp - now_ns) > MAX_TIME_DIFF_NS) {
            LOG_WARN("[MQTTDataConverter]: timestamp too old or in future: %lld vs %lld", 
                     packet.timestamp, now_ns);
            return false;
        }
        
        return true;
    }
    
    /**
     * 验证WTInfo数据的有效性
     */
    static bool validateWTInfo(const WTInfo& info) {
        // 检查时间差的合理性
        const int32_t MAX_DIFF_SECONDS = 24 * 3600; // 24小时
        if (abs(info.diff_seconds.load()) > MAX_DIFF_SECONDS) {
            LOG_WARN("[MQTTDataConverter]: WTInfo diff_seconds out of range: %d", 
                     info.diff_seconds.load());
            return false;
        }
        
        // 检查纳秒数的有效性
        if (abs(info.diff_nanoseconds.load()) >= 1000000000) {
            LOG_WARN("[MQTTDataConverter]: WTInfo diff_nanoseconds out of range: %d", 
                     info.diff_nanoseconds.load());
            return false;
        }
        
        // 检查同步状态的有效性
        WTSyncStatus wt_status = info.wt_sync_status.load();
        if (wt_status < WT_STATE_INITIALIZED || wt_status > WT_STATE_FROM_HTTP) {
            LOG_WARN("[MQTTDataConverter]: invalid wt_sync_status: %d", static_cast<int>(wt_status));
            return false;
        }
        
        OTMasterSyncStatus ot_status = info.ot_master_sync_status.load();
        if (ot_status < OT_MASTER_NOT_SYNCHRONIZED || ot_status > OT_MASTER_SYNCHRONIZED) {
            LOG_WARN("[MQTTDataConverter]: invalid ot_master_sync_status: %d", static_cast<int>(ot_status));
            return false;
        }
        
        return true;
    }

    /**
     * 从JSON字符串转换为WTInfo
     */
    static WTInfo convertJsonToWTInfo(const std::string& json_str) {
        WTInfo info{};
        
        try {
            // 简单的JSON解析（这里使用简化版本，实际项目中建议使用专业的JSON库）
            // 查找各个字段
            size_t pos = 0;
            
            // 解析diff_seconds
            pos = json_str.find("\"diff_seconds\":");
            if (pos != std::string::npos) {
                pos += 15; // 跳过"diff_seconds":
                while (pos < json_str.length() && (json_str[pos] == ' ' || json_str[pos] == '\t')) pos++;
                info.diff_seconds = std::stoi(json_str.substr(pos));
            }
            
            // 解析diff_nanoseconds
            pos = json_str.find("\"diff_nanoseconds\":");
            if (pos != std::string::npos) {
                pos += 19; // 跳过"diff_nanoseconds":
                while (pos < json_str.length() && (json_str[pos] == ' ' || json_str[pos] == '\t')) pos++;
                info.diff_nanoseconds = std::stoi(json_str.substr(pos));
            }
            
            // 解析ot_sync_status
            pos = json_str.find("\"ot_sync_status\":");
            if (pos != std::string::npos) {
                pos += 17; // 跳过"ot_sync_status":
                while (pos < json_str.length() && (json_str[pos] == ' ' || json_str[pos] == '\t')) pos++;
                int status = std::stoi(json_str.substr(pos));
                info.ot_master_sync_status = static_cast<OTMasterSyncStatus>(status);
            }
            
            info.wt_sync_status = WT_STATE_SYNCHRONIZED; // MQTT数据源认为是同步的
            
            LOG_INFO("[MQTTDataConverter]: converted JSON to WTInfo: "
                     "diff_seconds=%d, diff_nanoseconds=%d, ot_sync_status=%d",
                     info.diff_seconds.load(), info.diff_nanoseconds.load(),
                     static_cast<int>(info.ot_master_sync_status.load()));
                     
        } catch (const std::exception& e) {
            LOG_ERR("[MQTTDataConverter]: Failed to parse JSON: %s", e.what());
            info.wt_sync_status = WT_STATE_TIMEOUT;
            info.ot_master_sync_status = OT_MASTER_NOT_SYNCHRONIZED;
        }
        
        return info;
    }
    
    /**
     * 自动检测消息格式并转换
     */
    static WTInfo convertToWTInfo(const std::string& message, MQTTMessageFormat format) {
        if (format == MQTTMessageFormat::JSON) {
            return convertJsonToWTInfo(message);
        } else {
            // 二进制格式
            if (message.size() >= sizeof(MQTTDiffPacket)) {
                const MQTTDiffPacket* packet = 
                    reinterpret_cast<const MQTTDiffPacket*>(message.data());
                
                // 验证数据包
                if (validatePacket(*packet)) {
                    return convertBinaryToWTInfo(*packet);
                } else {
                    LOG_ERR("[MQTTDataConverter]: Binary packet validation failed");
                    WTInfo info{};
                    info.wt_sync_status = WT_STATE_TIMEOUT;
                    info.ot_master_sync_status = OT_MASTER_NOT_SYNCHRONIZED;
                    return info;
                }
            } else {
                LOG_ERR("[MQTTDataConverter]: Binary message too small: %zu bytes, expected: %zu", 
                        message.size(), sizeof(MQTTDiffPacket));
                WTInfo info{};
                info.wt_sync_status = WT_STATE_TIMEOUT;
                info.ot_master_sync_status = OT_MASTER_NOT_SYNCHRONIZED;
                return info;
            }
        }
    }
    
    /**
     * 自动检测消息格式
     */
    static MQTTMessageFormat detectMessageFormat(const std::string& message) {
        // 简单的格式检测：如果以'{'开头，认为是JSON
        if (!message.empty() && message[0] == '{') {
            return MQTTMessageFormat::JSON;
        }
        return MQTTMessageFormat::BINARY;
    }
};

} // namespace tmgr
