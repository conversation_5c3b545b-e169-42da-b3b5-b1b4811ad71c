#pragma once

#include "data_types.h"
#include "ot_consumer.h"
#include "tmlog.h"

namespace tmgr {
struct HttpDataConverter {
  static WTInfo convertToWTInfo(int64_t server_time_stamp_nanoseconds_count) {
    WTInfo info{};
    auto ot_time = OTConsumer::getInstance().GetCurrentTime();
    int64_t diff_nanoseconds = server_time_stamp_nanoseconds_count -
                               ot_time.time_since_epoch().count();

    info.diff_seconds = static_cast<int32_t>(diff_nanoseconds / 1000000000);
    info.diff_nanoseconds = static_cast<int32_t>(diff_nanoseconds % 1000000000);
    LOG_INFO(
        "[HTTPDataConverter]: server's timestamp diff with current ot(%lld): "
        "%lld "
        "nanoseocnds convert into diff_seconds is %d, diff_nanoseconds is %d",
        ot_time.time_since_epoch().count(), diff_nanoseconds,
        info.diff_seconds.load(), info.diff_nanoseconds.load());
    info.wt_sync_status =
        WT_STATE_FROM_HTTP; // TODO: is this necessary to be single or use with
                            // timeout together?
    info.ot_master_sync_status = OT_MASTER_NOT_SYNCHRONIZED;

    return info;
  }
};
} // namespace tmgr