#include "time_file_handler.h"
#include "data_types.h"
#include "inttypes.h"
#include "system_clock_utility.h"
#include "tmlog.h"
#include <chrono>
#include <cstdarg>
#include <cstring>
#include <fstream>
#include <iostream>
#include <regex>
#include <string>

namespace tmgr {

int tm_print(FILE *stream, const char *format, ...) {
  va_list args;
  va_start(args, format);
  int result = vfprintf(stream, format, args);
  va_end(args);
  return result;
}

TimeFileHandler::TimeFileHandler(const std::string &filePath)
    : filePath_(filePath) {}

Timestamp TimeFileHandler::readTime() const { return readTimeWithLog(true); }

Timestamp TimeFileHandler::readTimeWithLog(bool logTime) const {
  std::ifstream file(filePath_);
  std::string line;
  if (file.is_open() && std::getline(file, line) &&
      line.find("SystemClock: ") != std::string::npos) {

    auto ns_str = line.substr(strlen("SystemClock: "));
    if (logTime) {
      LOG_INFO("[TimeFileHandler]: Read system clock %s (%s) from file %s",
               ns_str.c_str(),
               tmgr::SystemClockUtility::prettyStrTimePoint(
                   Timestamp(std::chrono::nanoseconds(std::stoll(ns_str))))
                   .c_str(),
               filePath_.c_str());
    }
    try {
      auto ns = std::stoll(ns_str);
      return Timestamp(std::chrono::nanoseconds(ns));
    } catch (const std::invalid_argument &e) {
      fprintf(stderr, "[TimeFileHandler]: Invalid argument: %s\n", e.what());
    } catch (const std::out_of_range &e) {
      fprintf(stderr, "[TimeFileHandler]: Out of range: %s\n", e.what());
    }
  } else {
    LOG_WARN(
        "[TimeFileHandler]: Failed to read system clock from file %s, return "
        "current Timestamp(0), err reason is %s",
        filePath_.c_str(), strerror(errno));
  }

  return Timestamp(std::chrono::nanoseconds(0));
}

// current wrtie WT info file, maybe choose string or whatever
void TimeFileHandler::writeTime(const Timestamp &timePoint) const {
  if (timePoint.time_since_epoch().count() <= 0) {
    LOG_WARN(
        "[TimeFileHandler]: timePoint less / equal than 0, invalid value, no "
        "need to write timestamp to file");
    return;
  }

  auto current_timestamp_from_file = readTimeWithLog(false);

  if (timePoint > current_timestamp_from_file) {

    FILE *file = fopen(filePath_.c_str(), "w");

    if (file != nullptr) {
      fprintf(file, "SystemClock: %" PRIu64 "\n",
              (uint64_t)timePoint.time_since_epoch().count());
      fprintf(file, "%s\n",
              SystemClockUtility::prettyStrTimePoint(timePoint).c_str());
      fclose(file);

      LOG_DEBUG(
          "[TimeFileHandler]: Wrote timestamp to file %s, value is %lld (%s)",
          filePath_.c_str(), timePoint.time_since_epoch().count(),
          tmgr::SystemClockUtility::prettyStrTimePoint(timePoint).c_str());
    } else {
      LOG_ERR(
          "[TimeFileHandler]: Failed to write timestamp to file %s, value is "
          "%lld (%s), err reason is %s",
          filePath_.c_str(), timePoint.time_since_epoch().count(),
          tmgr::SystemClockUtility::prettyStrTimePoint(timePoint).c_str(),
          strerror(errno));
    }
  } else {
    LOG_WARN("[TimeFileHandler]: no need to update time record file, as "
             "timepoint %lld (%s) is less than current time record %lld (%s)",
             timePoint.time_since_epoch().count(),
             tmgr::SystemClockUtility::prettyStrTimePoint(timePoint).c_str(),
             current_timestamp_from_file.time_since_epoch().count(),
             tmgr::SystemClockUtility::prettyStrTimePoint(
                 current_timestamp_from_file)
                 .c_str());
  }
}

bool TimeFileHandler::hasValidTime() const {
  std::ifstream file(filePath_);
  std::string line;

  if (file.is_open() && std::getline(file, line)) {
    if (SystemClockUtility::isValidTimeFormat(line)) {
      LOG_INFO("[TimeFileHandler]: File %s contains a valid time format",
               filePath_.c_str());
      return true;
    } else {
      LOG_WARN(
          "[TimeFileHandler]: File %s does not contain a valid time format",
          filePath_.c_str());
      return false;
    }
  } else {
    LOG_WARN("[TimeFileHandler]: Unable to open the file %s (no exist / "
             "permission issue) or file content is empty",
             filePath_.c_str());
    return false;
  }
}

std::string TimeFileHandler::path() const { return filePath_; }

} // namespace tmgr