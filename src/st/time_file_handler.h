#pragma once

#include "data_types.h"
#include <chrono>
#include <string>

namespace tmgr {

int tm_print(FILE *stream, const char *format, ...);

class TimeFileHandler {
public:
  TimeFileHandler(const std::string &filePath);
  bool hasValidTime() const;
  Timestamp readTime() const;
  void writeTime(const Timestamp &timePoint) const;
  std::string path() const;

private:
  Timestamp readTimeWithLog(bool logTime) const;

private:
  std::string filePath_;
};
} // namespace tmgr