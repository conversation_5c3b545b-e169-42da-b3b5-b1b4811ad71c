#include "system_clock_setter.h"
#include "data_types.h"
#include "system_clock_utility.h"
#include "tmlog.h"
#include <cstring>
#include <sys/time.h>

namespace tmgr {

bool SystemClockSetter::setSystemTime(const Timestamp &timestamp) {
  if (timestamp.time_since_epoch().count() <= 0) {
    LOG_ERR("[SystemTimeSetter]: set system time failed with invalid timestamp "
            "which count is <= 0");
    return false;
  }
  auto timePoint =
      SystemClockUtility::convertTimestampNsToSystemClockUs(timestamp);
  std::time_t newTime = std::chrono::system_clock::to_time_t(timePoint);

  auto microsecondsPart = std::chrono::duration_cast<std::chrono::microseconds>(
                              timestamp.time_since_epoch()) %
                          1000000;

  struct timeval tv;
  tv.tv_sec = newTime;
  tv.tv_usec = static_cast<suseconds_t>(microsecondsPart.count());

  if (settimeofday(&tv, NULL) != 0) {
    LOG_ERR("[SystemTimeSetter]: set system time failed with seconds part is "
            "%lld and "
            "microseconds part is %lld, err reason is %s",
            newTime, microsecondsPart.count(), strerror(errno));

    return false;
  } else {

    LOG_INFO(
        "[SystemTimeSetter]: set system time success with seoncds part is %lld "
        "and microseconds part is %lld (%s)",
        newTime, microsecondsPart.count(),
        SystemClockUtility::prettyStrTimePoint(timestamp).c_str());

    return true;
  }
}

bool SystemClockSetter::setSystemTime(
    const std::chrono::system_clock::time_point &timePoint) {
  auto tm = SystemClockUtility::convertSystemClockUsToTimestampNs(timePoint);
  return setSystemTime(tm);
}

} // namespace tmgr