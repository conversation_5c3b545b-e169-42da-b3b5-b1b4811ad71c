#include "build_time_generator.h"
#include "tmlog.h"
#include <ctime>
#include <iomanip>
#include <sstream>
#include <string>

namespace tmgr {
std::chrono::system_clock::time_point BuildTimeGenerator::getBuildTime() {
  std::string buildDate = __DATE__; // "Mmm dd yyyy"
  std::string buildTime = __TIME__; // "hh:mm:ss"

  // in some system, should be leader 0 to make get_time work properly
  if (buildDate[3] == ' ' && buildDate[4] == ' ') {
    buildDate[4] = '0';
  }

  std::tm buildTm = {};
  std::istringstream dateStream(buildDate + " " + buildTime);
  dateStream >> std::get_time(&buildTm, "%b %d %Y %H:%M:%S");

  if (dateStream.fail()) {
    LOG_ERR("[BuildTimeGenerator]: datastream error, failed to parse build "
            "time, fallback to epoch");
    return std::chrono::system_clock::from_time_t(0);
  }

  auto timeT = std::mktime(&buildTm);
  if (timeT == -1) {
    LOG_ERR("[BuildTimeGenerator]: mktime conversion failed, cannot set "
            "build time");
    return std::chrono::system_clock::from_time_t(0); // 或者其他适当的错误处理
  }

  // 减去8小时以转换为UTC时间（东八区）
  constexpr int offsetSeconds = 8 * 60 * 60; // 8小时的秒数
  timeT -= offsetSeconds;

  return std::chrono::system_clock::from_time_t(timeT);
}
} // namespace tmgr
