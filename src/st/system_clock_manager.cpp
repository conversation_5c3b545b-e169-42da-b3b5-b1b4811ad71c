#include "system_clock_manager.h"
#include "build_time_generator.h"
#include "data_types.h"
#include "ot_consumer.h"
#include "system_clock_utility.h"
#include "tmlog.h"
#include "wt_calculator.h"
#include <chrono>
#ifdef TM_USE_DM
#include "monitor/dm_monitor.h"
#endif

namespace tmgr {
SystemClockManager::SystemClockManager(const std::string &filePath,
                                       int diskWriteIntervalMillSec)
    : timeFileHandler_(filePath),
      diskWriteIntervalMillSec_(diskWriteIntervalMillSec) {
  initSystemTime();
  startDiskWriteTimer();
}

// TODO: just check first flag and feed value
bool SystemClockManager::updateSystemClockFirstTimeWithWTInfo(
    int64_t diff_in_nanoseconds) {
  if (needFirstTimeUpdateSystemTime_.load()) {
    LOG_INFO(
        "[SystemClockManager]: begin to update system time with wt diff(ns) "
        "%lld from someip",
        diff_in_nanoseconds);

    auto ot_time = OTConsumer::getInstance().GetCurrentTime();

    if (ot_time.time_since_epoch().count() <= 0) {
      LOG_ERR("[SystemClockManager]: OT time is invalid which is <= 0, need "
              "wait for "
              "TimeSyncDaemon back to normal");
      return false;
    }

    // make sure OT time must be valid
    auto wt_diff_duration_nano = std::chrono::nanoseconds(diff_in_nanoseconds);
    obf::ts::Timestamp newTimePoint = ot_time + wt_diff_duration_nano;
    LOG_INFO("newTimePoint calculate: %lld, which is %lld(ot) + %lld(diff)",
             newTimePoint.time_since_epoch().count(),
             ot_time.time_since_epoch().count(), wt_diff_duration_nano.count());

    auto newSystemTime = Timestamp(
        std::chrono::nanoseconds(newTimePoint.time_since_epoch().count()));

    // NOTE: cannot change high_resolution_clock to system_clock to try to
    // compile because in QNX, high_resolution calculation process is not the
    // same as system_clock and also you cannot revert high_resolution_clock
    // back to system_clock, so in this place we can just use system_clock and
    // ignore nanoseconds part to calculate with newTimePoint
    auto currentSystemTime =
        Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::system_clock::now().time_since_epoch()));
    // when wt diff + ot > current system time, update system time once
    if (newSystemTime > currentSystemTime) {
      LOG_INFO(
          "[SystemClockManager]: Init update - wtTime %lld (%s) is bigger than "
          "current "
          "system time %lld (%s), begin to update system time with wt time",
          newSystemTime.time_since_epoch().count(),
          SystemClockUtility::prettyStrTimePoint(newSystemTime).c_str(),
          currentSystemTime.time_since_epoch().count(),
          SystemClockUtility::prettyStrTimePoint(currentSystemTime).c_str());
      setSystemClock(newSystemTime);
    } else {
      LOG_INFO(
          "[SystemClockManager]: Init update - wtTime %lld (%s) is less than "
          "current system "
          "time %lld (%s), no need to update system time",
          newSystemTime.time_since_epoch().count(),
          SystemClockUtility::prettyStrTimePoint(newSystemTime).c_str(),
          currentSystemTime.time_since_epoch().count(),
          SystemClockUtility::prettyStrTimePoint(currentSystemTime).c_str());
    }
    needFirstTimeUpdateSystemTime_.store(false);
  } else {
    LOG_INFO("[SystemClockManager]: Init update - no need to update system "
             "time for first "
             "time init, has setted");
  }

  return true;
}

void SystemClockManager::updateSystemClockRegularWithWTInfo(
    int64_t diff_in_nanoseconds) {

  auto ot_time = OTConsumer::getInstance().GetCurrentTime();

  if (ot_time.time_since_epoch().count() <= 0) {
    LOG_ERR("[SystemClockManager]: OT time is invalid which is <= 0, need "
            "wait for "
            "TimeSyncDaemon back to normal");
    return;
  }

  auto wt_diff_duration_nano = std::chrono::nanoseconds(diff_in_nanoseconds);
  obf::ts::Timestamp newTimePoint = ot_time + wt_diff_duration_nano;
  LOG_DEBUG("newTimePoint calculate: %lld, which is %lld(ot) + %lld(diff)",
            newTimePoint.time_since_epoch().count(),
            ot_time.time_since_epoch().count(), wt_diff_duration_nano.count());

  auto newSystemTime = Timestamp(
      std::chrono::nanoseconds(newTimePoint.time_since_epoch().count()));

  // NOTE: cannot change high_resolution_clock to system_clock to try to
  // compile because in QNX, high_resolution calculation process is not the
  // same as system_clock and also you cannot revert high_resolution_clock
  // back to system_clock, so in this place we can just use system_clock and
  // ignore nanoseconds part to calculate with newTimePoint
  auto currentSystemTime =
      Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
          std::chrono::system_clock::now().time_since_epoch()));

  // when wt diff + ot > current system time + 5s, update system time
  if ((newSystemTime - currentSystemTime) >=
      std::chrono::seconds(kUpdateThresholdSeconds)) {
    LOG_INFO("[SystemClockManager]: Regular update - wtTime %lld (%s) is [5s] "
             "bigger than current "
             "system time %lld (%s), begin to update system time with wt time",
             newSystemTime.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(newSystemTime).c_str(),
             currentSystemTime.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(currentSystemTime).c_str());
    setSystemClock(newSystemTime);
  } else {
    LOG_INFO("[SystemClockManager]: Regular update - wtTime %lld (%s) is less "
             "than [5s] compared "
             "with current system "
             "time %lld (%s), no need to update system time",
             newSystemTime.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(newSystemTime).c_str(),
             currentSystemTime.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(currentSystemTime).c_str());
  }
}

void SystemClockManager::setSystemClock(const Timestamp &timePoint) {
  // TODO: check if equal 0 is valid or not
  if (timePoint.time_since_epoch().count() <= 0) {
    LOG_ERR("[SystemClockManager]: timePoint less/equal than 0, invalid value, "
            "no need to "
            "set system time");
    return;
  }

  // as sometimes, TimeManager will restart serveal times beyond 1970 for
  // testing cases, we should try to note let time back to protect for infinite
  // waiting
  auto currentSysTime =
      Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
          std::chrono::system_clock::now().time_since_epoch()));

  // check currentSysTime is newer that timePoint, system time *cannot* be
  // rolled back
  if (currentSysTime > timePoint) {
    LOG_WARN("[SystemClockManager]: current system time %lld (GMT %s) is newer "
             "than timePoint %lld (GMT %s), "
             "no need to set system time",
             currentSysTime.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(currentSysTime).c_str(),
             timePoint.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(timePoint).c_str());
  } else {
    LOG_INFO("[SystemClockManager]: after checking timepoint validity, begin "
             "to set system "
             "clock from %lld (GMT %s) to %lld (GMT %s)",
             currentSysTime.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(currentSysTime).c_str(),
             timePoint.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(timePoint).c_str());
    systemTimeSetter_.setSystemTime(timePoint);
  }
}

// NOTE: this will seralize WT time into disk not system clock
void SystemClockManager::startDiskWriteTimer() {
  diskWriteTimer_.start(diskWriteIntervalMillSec_, [this]() {
    auto time_point = WTCalculator::calculateWTTime(true);

#ifdef TM_USE_DM
    // for DTC purpose
    checkOTTimeValid();
    checkWTTimeValid(time_point.time_since_epoch().count());
#endif

    auto tp_count = time_point.time_since_epoch().count();
    if (tp_count <= 0) {
      LOG_WARN(
          "[SystemClockManager]: timePoint less/equal than 0, which is %lld, "
          "invalid value, no need to "
          "write to file",
          tp_count);
      return;
    }

    LOG_INFO("[SystemClockManager]: begin to write timestamp(ns) to file %s "
             "with count "
             "%lld, GMT time is %s",
             timeFileHandler_.path().c_str(),
             time_point.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(time_point).c_str());

    timeFileHandler_.writeTime(time_point);
  });
}
void SystemClockManager::checkOTTimeValid() {
#ifdef TM_USE_DM
  auto ot_time = OTConsumer::getInstance().GetCurrentTime();
  if (DMMonitor::getInstance().isDMEnabled()) {
    if (ot_time.time_since_epoch().count() <= 0) {
      DMMonitor::getInstance().reportDMErrorCode(
          DMErrorCode::DM_ERR_TM_GET_OT_ERROR, true);
    } else {
      DMMonitor::getInstance().reportDMErrorCode(
          DMErrorCode::DM_ERR_TM_GET_OT_ERROR, false);
    }
  }
#endif
}

void SystemClockManager::checkWTTimeValid(int64_t nano_count) {
#ifdef TM_USE_DM
  // we should compare nano_count to 1980, if we receive a time point less than
  // it, we should treat it as error
  static const int64_t NANO_1980 = 315532800ULL * 1000000000ULL;

  if (DMMonitor::getInstance().isDMEnabled()) {
    if (nano_count <= NANO_1980) {
      DMMonitor::getInstance().reportDMErrorCode(
          DMErrorCode::DM_ERR_TM_UTC_INVALID, true);
    } else {
      DMMonitor::getInstance().reportDMErrorCode(
          DMErrorCode::DM_ERR_TM_UTC_INVALID, false);
    }
  }
#endif
}

SystemClockManager::~SystemClockManager() { diskWriteTimer_.stop(); }

void SystemClockManager::initSystemTime() {
  // read from file
  if (timeFileHandler_.hasValidTime()) {
    LOG_INFO("[SystemClockManager]: begin to read time from file, and set it "
             "into sys");
    setSystemClock(timeFileHandler_.readTime());
  } else {
    // read from build time
    LOG_INFO("[SystemClockManager]: begin to read time from building time, and "
             "set it "
             "into sys");

    auto buildTimePoint = BuildTimeGenerator::getBuildTime();

    LOG_INFO("[SystemClockManager]: build time is %lld, pretty print is %s",
             buildTimePoint.time_since_epoch().count(),
             SystemClockUtility::prettyStrTimePoint(buildTimePoint).c_str());

    auto timestamp =
        Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
            buildTimePoint.time_since_epoch()));

    setSystemClock(timestamp);
  }
}

bool SystemClockManager::isTimePointFutureThanSystemClock(
    const Timestamp &timePoint) {
  auto currentSystemTime =
      Timestamp(std::chrono::duration_cast<std::chrono::nanoseconds>(
          std::chrono::system_clock::now().time_since_epoch()));
  // when wt diff + ot > current system time, update system time once
  if (timePoint > currentSystemTime) {
    return true;
  } else {
    return false;
  }
}

} // namespace tmgr
