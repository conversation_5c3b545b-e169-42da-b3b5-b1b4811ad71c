#include <iostream>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <unistd.h>
#include <cstring>
#include <chrono>
#include <thread>

// 与UDP时间通道相同的数据包结构
struct UDPDiffPacket {
    int32_t diff_seconds;
    int32_t diff_nanoseconds;
    uint8_t ot_sync_status;
    uint8_t reserved[3];  // 对齐填充
    int64_t timestamp;    // 可选时间戳
} __attribute__((packed));

void printUsage(const char* progName) {
    std::cout << "Usage: " << progName << " [options]\n"
              << "Options:\n"
              << "  -h host      Target host IP (default: *********)\n"
              << "  -p port      Target port (default: 2381)\n"
              << "  -s seconds   Diff seconds (default: 5)\n"
              << "  -n nanos     Diff nanoseconds (default: 500000000)\n"
              << "  -o status    OT sync status (default: 2)\n"
              << "  -i interval  Send interval in seconds (default: 2)\n"
              << "  -c count     Number of packets to send (default: 10, 0 = infinite)\n"
              << "  --help       Show this help\n";
}

int main(int argc, char* argv[]) {
    std::string host = "*********";
    int port = 2381;
    int32_t diff_seconds = 5;
    int32_t diff_nanoseconds = 500000000;
    uint8_t ot_sync_status = 2;
    int send_interval = 2;
    int send_count = 10;

    // 解析命令行参数
    for (int i = 1; i < argc; i++) {
        if (strcmp(argv[i], "-h") == 0 && i + 1 < argc) {
            host = argv[++i];
        } else if (strcmp(argv[i], "-p") == 0 && i + 1 < argc) {
            port = std::atoi(argv[++i]);
        } else if (strcmp(argv[i], "-s") == 0 && i + 1 < argc) {
            diff_seconds = std::atoi(argv[++i]);
        } else if (strcmp(argv[i], "-n") == 0 && i + 1 < argc) {
            diff_nanoseconds = std::atoi(argv[++i]);
        } else if (strcmp(argv[i], "-o") == 0 && i + 1 < argc) {
            ot_sync_status = std::atoi(argv[++i]);
        } else if (strcmp(argv[i], "-i") == 0 && i + 1 < argc) {
            send_interval = std::atoi(argv[++i]);
        } else if (strcmp(argv[i], "-c") == 0 && i + 1 < argc) {
            send_count = std::atoi(argv[++i]);
        } else if (strcmp(argv[i], "--help") == 0) {
            printUsage(argv[0]);
            return 0;
        } else {
            std::cerr << "Unknown option: " << argv[i] << std::endl;
            printUsage(argv[0]);
            return 1;
        }
    }

    // 创建UDP套接字
    int sockfd = socket(AF_INET, SOCK_DGRAM, 0);
    if (sockfd < 0) {
        std::cerr << "Failed to create socket: " << strerror(errno) << std::endl;
        return 1;
    }

    // 设置目标地址
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    
    if (inet_pton(AF_INET, host.c_str(), &server_addr.sin_addr) <= 0) {
        std::cerr << "Invalid IP address: " << host << std::endl;
        close(sockfd);
        return 1;
    }

    std::cout << "UDP Test Client\n"
              << "Target: " << host << ":" << port << "\n"
              << "Diff: " << diff_seconds << "s " << diff_nanoseconds << "ns\n"
              << "OT Status: " << static_cast<int>(ot_sync_status) << "\n"
              << "Interval: " << send_interval << "s\n"
              << "Count: " << (send_count == 0 ? "infinite" : std::to_string(send_count)) << "\n"
              << "Packet size: " << sizeof(UDPDiffPacket) << " bytes\n"
              << "Starting to send packets...\n\n";

    int sent_count = 0;
    while (send_count == 0 || sent_count < send_count) {
        // 准备数据包
        UDPDiffPacket packet;
        packet.diff_seconds = diff_seconds;
        packet.diff_nanoseconds = diff_nanoseconds;
        packet.ot_sync_status = ot_sync_status;
        memset(packet.reserved, 0, sizeof(packet.reserved));
        packet.timestamp = std::chrono::duration_cast<std::chrono::nanoseconds>(
            std::chrono::system_clock::now().time_since_epoch()).count();

        // 发送数据包
        ssize_t bytes_sent = sendto(sockfd, &packet, sizeof(packet), 0,
                                   (struct sockaddr*)&server_addr, sizeof(server_addr));
        
        if (bytes_sent < 0) {
            std::cerr << "Failed to send packet: " << strerror(errno) << std::endl;
            break;
        } else if (bytes_sent != sizeof(packet)) {
            std::cerr << "Partial send: " << bytes_sent << "/" << sizeof(packet) << " bytes" << std::endl;
        } else {
            sent_count++;
            std::cout << "Packet " << sent_count << " sent successfully: "
                      << "diff=" << diff_seconds << "s " << diff_nanoseconds << "ns, "
                      << "ot_status=" << static_cast<int>(ot_sync_status) << ", "
                      << "timestamp=" << packet.timestamp << std::endl;
        }

        // 等待指定间隔
        if (send_count == 0 || sent_count < send_count) {
            std::this_thread::sleep_for(std::chrono::seconds(send_interval));
        }

        // 可以添加一些变化来模拟真实环境
        diff_nanoseconds += 1000000; // 每次增加1ms
        if (diff_nanoseconds >= 1000000000) {
            diff_nanoseconds -= 1000000000;
            diff_seconds++;
        }
    }

    close(sockfd);
    std::cout << "\nTotal packets sent: " << sent_count << std::endl;
    return 0;
} 