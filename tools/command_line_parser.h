#pragma once

#include <map>
#include <set>
#include <string>
#include <vector>

namespace tmgr {

class CommandLineParser {
public:
  CommandLineParser(int argc, char **argv);
  std::string getOption(const std::string &optionName) const;
  bool hasOption(const std::string &optionName) const;
  bool hasFlag(const std::string &flagName) const;
  bool isValid() const;
  std::vector<std::string> getCommands() const;
  std::map<std::string, std::string> getOptions() const;
  std::set<std::string> getFlags() const;

private:
  void parse(int argc, char **argv);

private:
  std::map<std::string, std::string> options;
  std::set<std::string> flags;
  std::vector<std::string> commands;
  bool valid = true;
};
} // namespace tmgr