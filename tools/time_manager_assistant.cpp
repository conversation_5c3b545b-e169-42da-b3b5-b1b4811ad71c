#include "command_line_parser.h"
#include "data_types.h"
#include "tmlog.h"
#include <chrono>
#ifdef TM_USE_TIME_SYNC
#include "obf/tsync/synchronized_time_base_status.h"
#include "iwt_statuscheck.h"
#include "http_time_channel.h"
#endif
#include "system_clock_utility.h"
#include "time_manager_consumer.h"
#include <thread>
#include <inttypes.h>
#include "utility.hpp"
#include <cstdio>

void printUsage() {
  fprintf(
      stderr,
      "\nUsage: ./%s <command> <options>\n"
      " ottime            prints ot time with nanoseconds\n"
      #ifdef TM_USE_TIME_SYNC
      " wttime            prints wt time with nanoseconds\n"
      " otstatus          prints ot synchronization status and creation time\n"
      " otmasterstatus   prints ot master synchronization status\n"
      " wtstatus          prints wt synchronization status\n"
      " http          prints if current ADCU can connect to server(defualt baidu.com) and get timestamp from server\n"
      " monitor          prints ot and wt time and status in a loop\n"
      " steady          prints steady clock count and GMT time\n"
      " -t <seconds>    specify the sleep duration for monitor mode, default "
      "is 12s\n"
      " diffsec         prints the difference in seconds between the WT and "
      "the OT\n"
      " diffnanosec        prints the difference in nanoseconds between the WT "
      "and the OT\n"
      " -s <specifier>  specify service specifier\n"
      " -u <url>        get timestamp from server url within http protocol, defualt is baidu.com\n"
      #endif
      " alive            check if TimeManager Daemon is alive or not(default threshold is 2000ms, you can change with -a xxx)\n"
      " mode             prints the current running mode\n"
      " -w <shared memory node> shared memory node name to communcate with TimeManager Daemon\n"
      " -v              verbose Debug info log and output to screen\n"
      " -l <log_file>   specify log file into this log with verbose log info\n"
      " -a <num_millseconds>        set the threshold milliseconds count when check in alive, default value is 2000ms\n"
      " -h              print usage\n"
      "\n",
      "time_manager_assistant");
}

#ifdef TM_USE_TIME_SYNC
static std::string syncStatusToStr(obf::ts::SynchronizationStatus status) {
  std::string str;
  switch (status) {
  case obf::ts::SynchronizationStatus::kNotSynchronizedUntilStartup:
    str = "Not Sync";
    break;
  case obf::ts::SynchronizationStatus::kSynchronized:
    str = "Synced";
    break;
  case obf::ts::SynchronizationStatus::kSynchToGateway:
    str = "Synced Gateway";
    break;
  case obf::ts::SynchronizationStatus::kTimeOut:
    str = "Timeout";
  default:
    str = "Unknown";
    break;
  }
  return str;
};

const std::string leapJumpToStr(obf::ts::LeapJump leap) {
  std::string str;
  switch (leap) {
  case obf::ts::LeapJump::kTimeLeapNone:
    str = "None";
    break;
  case obf::ts::LeapJump::kTimeLeapFuture:
    str = "Leap Future";
    break;
  case obf::ts::LeapJump::kTimeLeapPast:
    str = "Leap Past";
    break;
  default:
    str = "Unknown";
    break;
  }
  return str;
};

const std::string wtSyncStatusToStr(tmgr::WTSyncStatus status) {
  std::string str;
  switch (status) {
  case tmgr::WTSyncStatus::WT_STATE_INITIALIZED:
    str = "Initialized";
    break;
  case tmgr::WTSyncStatus::WT_STATE_TIMEOUT:
    str = "Timeout";
    break;
  case tmgr::WTSyncStatus::WT_STATE_JUMPED:
    str = "Jumped";
    break;
  case tmgr::WTSyncStatus::WT_STATE_SYNCHRONIZED:
    str = "Synchronized";
    break;
  case tmgr::WTSyncStatus::WT_STATE_FROM_HTTP:
    str = "From HTTP";
    break;
  default:
    str = "Unknown";
    break;
  }
  return str;
};

static void printWTStatus(const tmgr::WTSyncStatus &status) {
  fprintf(stderr, "%s\n", wtSyncStatusToStr(status).c_str());
}

static void printOTStatus(const obf::ts::SynchronizedTimeBaseStatus &status) {
  auto sync = status.GetSynchronizationStatus();
  auto leap = status.GetLeapJump();
  auto timestamp = status.GetCreationTime();
  fprintf(stderr, "%s, %s, %lld\n", syncStatusToStr(sync).c_str(),
          leapJumpToStr(leap).c_str(), timestamp.time_since_epoch().count());
}

static void printOTMasterStatus(const tmgr::OTMasterSyncStatus &status) {
  fprintf(stderr, "%s\n",
          tmgr::Utility::OTMasterSyncStatusToString(status).c_str());
}
#endif

int main(int argc, char **argv) {
  tmgr::CommandLineParser parser(argc, argv);

  if (!parser.isValid()) {
    printUsage();
    return 1;
  }

  if (parser.hasFlag("-h")) {
    printUsage();
    exit(0);
  }

  auto log_info_level_verbose = parser.hasFlag("-v");
  auto has_log_file_option = parser.hasOption("-l");
  std::string log_file;
  if (has_log_file_option) {
    log_file = parser.getOption("-l");
  }
  if (has_log_file_option && log_file.empty()) {
    fprintf(stderr, "No log file provided, should specific it with -l option, "
                    "e.g. -l /tmp/log.txt \n");
    printUsage();
    return 1;
  }

  if (log_info_level_verbose && !has_log_file_option) {
    tmgr::Logger::getInstance().init(tmgr::LOG_LEVEL_DEBUG, false);
  } else if (log_info_level_verbose && has_log_file_option) {
    tmgr::Logger::getInstance().init(tmgr::LOG_LEVEL_DEBUG, true, log_file);
  } else if (!log_info_level_verbose && has_log_file_option) {
    tmgr::Logger::getInstance().init(tmgr::LOG_LEVEL_INFO, true, log_file);
  } else {
    tmgr::Logger::getInstance().init(tmgr::LOG_LEVEL_INFO, false);
  }

  std::string ot_specifier, shared_memory_node;
  if (!parser.hasOption("-s")) {
    fprintf(stderr,
            "No ot specifier provided, specific it with default value ts_ot\n");
    ot_specifier = "ts_ot";
  } else {
    ot_specifier = parser.getOption("-s");
  }

  if (!parser.hasOption("-w")) {
    fprintf(stderr, "No shared memory node provided, specific it with default "
                    "value /ts_tm\n");
    shared_memory_node = "/ts_tm";
  } else {
    shared_memory_node = parser.getOption("-w");
  }

  fprintf(stderr,
          "[TimeManagerAssistant]: node config is ot specifier: %s, shared "
          "memory node: %s\n",
          ot_specifier.c_str(), shared_memory_node.c_str());

  // init TimeManager Client, *must before* any log here, to prevent log with ot count recursive
  tmgr::TimeManagerConsumer tmConsumer(ot_specifier, shared_memory_node);

  // dump commands / options / flags
  auto commands = parser.getCommands();
  for (const auto &command : commands) {
    LOG_DEBUG("Command: %s", command.c_str());
  }

  auto options = parser.getOptions();
  for (const auto &pair : options) {
    LOG_DEBUG("Option: %s = %s", pair.first.c_str(), pair.second.c_str());
  }

  auto flags = parser.getFlags();
  for (const auto &flag : flags) {
    LOG_DEBUG("Flag: %s", flag.c_str());
  }

  // current assistant just support one command run at one time
  if (commands.size() != 0) {
    auto cmd = commands[0];
    if (cmd == "ottime") {
      LOG_DEBUG("[TimeManagerAssistant]: in ottime command");
      auto res = tmConsumer.getOTTime();
      fprintf(stderr, "%" PRIu64 "\n", (uint64_t)res.time_since_epoch().count());
      fprintf(stderr, "%s\n",
              tmgr::SystemClockUtility::prettyStrTimePoint(res).c_str());
      fprintf(stderr, "steady clock count is %" PRIu64 "\n",
              (uint64_t)std::chrono::steady_clock::now().time_since_epoch().count());
    } 
    else if (cmd == "steady")
    {
      LOG_DEBUG("[TimeManagerAssistant]: in steady command");
      auto res = (uint64_t)std::chrono::steady_clock::now().time_since_epoch().count();
      fprintf(stderr, "%" PRIu64 "\n", res);
      fprintf(stderr, "%s\n",
              tmgr::SystemClockUtility::prettyStrTimePoint(tmgr::Timestamp(std::chrono::nanoseconds(res))).c_str());
    }
    #ifdef TM_USE_TIME_SYNC
    else if (cmd == "wttime") {
      LOG_DEBUG("[TimeManagerAssistant]: in wttime command");
      auto res = tmConsumer.getWTTime();
      fprintf(stderr, "%lld\n", res.time_since_epoch().count());
      fprintf(stderr, "%s\n",
              tmgr::SystemClockUtility::prettyStrTimePoint(res).c_str());
    } else if (cmd == "otstatus") {
      LOG_DEBUG("[TimeManagerAssistant]: in otstatus command");
      auto status = tmConsumer.getOTSyncStatus();
      printOTStatus(status);

    } else if (cmd == "otmasterstatus") {
      LOG_DEBUG("[TimeManagerAssistant]: in otmasterstatus command");
      auto status = tmConsumer.getOTMasterSyncStatus();
      printOTMasterStatus(status);
    } else if (cmd == "wtstatus") {
      LOG_DEBUG("[TimeManagerAssistant]: in wtstatus command");
      auto status = tmConsumer.getWTSyncStatus();
      printWTStatus(status);
    } else if (cmd == "diffsec") {
      LOG_DEBUG("[TimeManagerAssistant]: in diffsec command");
      auto diff = tmConsumer.getDiffSeconds();
      fprintf(stderr, "%d\n", diff);
    } else if (cmd == "diffnanosec") {
      LOG_DEBUG("[TimeManagerAssistant]: in diffnanosec command");
      auto diff = tmConsumer.getDiffNanoseconds();
      fprintf(stderr, "%d\n", diff);
    } else if (cmd == "http")
    {
      LOG_DEBUG("[TimeManagerAssistant]: in http command");
      std::string url;
      if (parser.hasOption("-u")) {
        url = parser.getOption("-u");
      } else {
        url = "www.baidu.com";
      }
      tmgr::HTTPTimeChannel httpChannel(
          url, std::make_shared<tmgr::DummyWTStatusChecker>());
      fprintf(stderr, "Connecting to %s\n", url.c_str());
      fprintf(stderr, "Server Response is: %s\n",
              httpChannel.serverDateResponse().c_str());
      fprintf(stderr, "Server Timestamp nanoseocnds count is %ld\n",
              httpChannel.timeStampNanoSecCount());
    }else if (cmd == "monitor") {
      LOG_DEBUG("[TimeManagerAssistant]: in monitor command");
      int sleep_duration = 12; // defualt value as someip channel from mpu its
                               // will send msg every 10s
      if (parser.hasOption("-t")) {
        std::string val = parser.getOption("-t");
        sleep_duration = std::stoi(val);
        if (sleep_duration <= 0) {
          sleep_duration = 12;
        }
      }

      fprintf(stderr,
              "================================== TimeMangerAssistant "
              "Monitor mode ===============================\n"
              "OTTime (diff with previous), OTStatus, WTTime (diff with "
              "previous), WTStatus, DiffSec (diff with previous), "
              "DiffNanoSec (diff with previous) will refresh every %d sec\n"
              "================================================================"
              "====================================\n",
              sleep_duration);
      auto previousOTTime = tmConsumer.getOTTime().time_since_epoch().count();
      auto previousWTTime = tmConsumer.getWTTime().time_since_epoch().count();
      auto previousDiffSec = tmConsumer.getDiffSeconds();
      auto previousDiffNanoSec = tmConsumer.getDiffNanoseconds();
      while (true) {
        auto currentTimeCount =
            tmConsumer.getOTTime().time_since_epoch().count();
        auto ot_diff = currentTimeCount - previousOTTime;
        fprintf(stderr, "OTTime: %lld", currentTimeCount);
        fprintf(stderr, " (%s%lld)", ot_diff > 0 ? "+" : "", ot_diff);

        auto otStatus = tmConsumer.getOTSyncStatus();
        fprintf(stderr, "     OTStatus: %s",
                syncStatusToStr(otStatus.GetSynchronizationStatus()).c_str());

        auto currentWtTime = tmConsumer.getWTTime().time_since_epoch().count();
        fprintf(stderr, "    WTTime: %lld", currentWtTime);
        auto wt_diff = currentWtTime - previousWTTime;
        fprintf(stderr, " (%s%lld)", wt_diff > 0 ? "+" : "", wt_diff);

        auto wtStatus = tmConsumer.getWTSyncStatus();
        fprintf(stderr, "    WTStatus: %s",
                wtSyncStatusToStr(wtStatus).c_str());

        auto diffSec = tmConsumer.getDiffSeconds();
        fprintf(stderr, "    DiffSec: %d", diffSec);
        auto sec_diff = diffSec - previousDiffSec;
        fprintf(stderr, " (%s%d)", sec_diff > 0 ? "+" : "", sec_diff);

        auto diffNanoSec = tmConsumer.getDiffNanoseconds();
        fprintf(stderr, "    DiffNanoSec: %d", diffNanoSec);
        auto nano_sec_diff = diffNanoSec - previousDiffNanoSec;
        fprintf(stderr, " (%s%d)\n", nano_sec_diff > 0 ? "+" : "",
                nano_sec_diff);

        previousOTTime = currentTimeCount;
        previousWTTime = currentWtTime;
        previousDiffSec = diffSec;
        previousDiffNanoSec = diffNanoSec;

        std::this_thread::sleep_for(std::chrono::seconds(sleep_duration));
      }
    }
#endif
    else if (cmd == "alive") {
      LOG_DEBUG("[TimeManagerAssistant]: in alive command");
      if (tmConsumer.isAlive()) {
        fprintf(stderr, "TimeManager Daemon is alive\n");
      } else {
        fprintf(stderr, "TimeManager Daemon is ***not*** alive\n");
      }
    } else if (cmd == "mode") {
      LOG_DEBUG("[TimeManagerAssistant]: in mode command");
      auto runningMode = tmConsumer.runningMode();
      fprintf(stderr, "Current running mode is %s\n",
              tmgr::Utility::RunningModeToString(runningMode).c_str());
    } else {
      fprintf(stderr, "Invalid command: %s not supported\n", cmd.c_str());
      printUsage();
    }
  } else {
    fprintf(stderr, "No command provided, should specify it with "
                    "[ottime, mode, alive, steady");

#ifdef TM_USE_TIME_SYNC
    fprintf(stderr, ", wttime, otstatus, otmasterstatus, wtstatus, http, monitor");
#endif

    fprintf(stderr, "] \n");
    printUsage();
    return 1;
  }

  return 0;
}
