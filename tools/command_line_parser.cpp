#include "command_line_parser.h"

namespace tmgr {

CommandLineParser::CommandLineParser(int argc, char **argv) {
  if (argc < 2) {
    fprintf(stderr,
            "Error input, invalid arguments, please input as follow:\n");
    valid = false;
    return;
  }
  parse(argc, argv);
}

std::string CommandLineParser::getOption(const std::string &optionName) const {
  auto it = options.find(optionName);
  if (it != options.end()) {
    return it->second;
  } else {
    fprintf(stderr, "Option %s not found", optionName.c_str());
    return "";
  }
}

bool CommandLineParser::hasOption(const std::string &optionName) const {
  return options.find(optionName) != options.end();
}

bool CommandLineParser::hasFlag(const std::string &flagName) const {
  return flags.find(flagName) != flags.end();
}

bool CommandLineParser::isValid() const { return valid; }

std::map<std::string, std::string> CommandLineParser::getOptions() const {
  return options;
}

std::set<std::string> CommandLineParser::getFlags() const { return flags; }

void CommandLineParser::parse(int argc, char **argv) {
  for (int i = 1; i < argc; ++i) {
    std::string arg = argv[i];

    if (arg[0] != '-') {
      commands.push_back(arg);
      continue;
    }

    if (arg[0] == '-') {
      if (i + 1 < argc && argv[i + 1][0] != '-') {
        options[arg] = argv[++i]; // 存储选项和对应的值
      } else {
        flags.insert(arg); // 将当前参数视为标志
      }
    }
  }

  if (options.empty() && flags.empty() && commands.empty()) {
    fprintf(stderr, "No valid options, flags, or commands provided.");
    valid = false;
  }
}

std::vector<std::string> CommandLineParser::getCommands() const {
  return commands;
}
}; // namespace tmgr
