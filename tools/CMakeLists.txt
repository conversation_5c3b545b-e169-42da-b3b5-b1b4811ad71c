add_executable(time_manager_assistant)
target_sources(time_manager_assistant PRI<PERSON><PERSON> time_manager_assistant.cpp
command_line_parser.cpp 
)

if (USE_TIME_SYNC)
target_sources(time_manager_assistant PRIVATE ${CMAKE_SOURCE_DIR}/src/communication/http_time_channel.cpp)
endif()

target_include_directories(time_manager_assistant PRIVATE ${CMAKE_SOURCE_DIR}/src/common
${CMAKE_SOURCE_DIR}/include/common
${CMAKE_SOURCE_DIR}/src/st
${CMAKE_SOURCE_DIR}/src/wt
${CMAKE_SOURCE_DIR}/src/communication
${CONAN_INCLUDE_DIRS_OBF_TIME_MANAGER_SDK}/common
${CONAN_INCLUDE_DIRS_OBF_TIME_MANAGER_SDK}/daemon
)

target_link_libraries(time_manager_assistant PRIVATE ${CONAN_LIBS_OBF_TIME_MANAGER_SDK} ${CONA<PERSON>_LIBS_OBF_TIME_SYNC_SDK})

if(NOT ${CMAKE_SYSTEM_NAME} STREQUAL "QNX")
    target_link_libraries(time_manager_assistant PRIVATE pthread rt)
endif()

install(TARGETS time_manager_assistant DESTINATION bin)
